#!/usr/bin/env python3
"""
Test database connection script
"""
import asyncio
import asyncpg
import sys
import os

# Add the project root to the Python path
sys.path.append(os.path.dirname(__file__))

from app.core.config import settings

async def test_connection():
    """Test the database connection"""

    try:
        print("Testing settings configuration...")
        print(f"DATABASE_URL: {settings.DATABASE_URL}")
        print(f"ASYNC_DATABASE_URL: {settings.ASYNC_DATABASE_URL}")
        print(f"DB_HOST: {settings.DB_HOST}")
        print(f"DB_PORT: {settings.DB_PORT}")
        print(f"DB_NAME: {settings.DB_NAME}")
        print(f"DB_USER: {settings.DB_USER}")

        # Database connection parameters from .env
        DB_HOST = "**************"
        DB_PORT = 4675
        DB_NAME = "postgres"
        DB_USER = "postgres"
        DB_PASSWORD = "GKq4oJsg86DuGW0fxlgpmmWoHjykyRJuEWjVPv2QIS8OMASsnxhe3wwSynTuffJP"

        print(f"\nAttempting to connect to database...")
        print(f"Host: {DB_HOST}")
        print(f"Port: {DB_PORT}")
        print(f"Database: {DB_NAME}")
        print(f"User: {DB_USER}")
        
        # Try to connect
        conn = await asyncpg.connect(
            host=DB_HOST,
            port=DB_PORT,
            database=DB_NAME,
            user=DB_USER,
            password=DB_PASSWORD
        )
        
        print("✅ Successfully connected to database!")
        
        # Test a simple query
        result = await conn.fetchval("SELECT version()")
        print(f"PostgreSQL version: {result}")
        
        # List databases
        databases = await conn.fetch("SELECT datname FROM pg_database WHERE datistemplate = false")
        print("Available databases:")
        for db in databases:
            print(f"  - {db['datname']}")
        
        await conn.close()
        print("✅ Connection test completed successfully!")
        
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        print(f"Error type: {type(e).__name__}")
        return False
    
    return True

if __name__ == "__main__":
    asyncio.run(test_connection())
