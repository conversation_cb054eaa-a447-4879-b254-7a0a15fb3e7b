#!/bin/bash

# Complete Admin/Agent Workflow Test with curl
# This script demonstrates the entire user roles and conversation assignment system

BASE_URL="http://localhost:8000"

echo "🚀 Yupcha Customer Bot AI - Complete Workflow Test"
echo "=================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_step() {
    echo -e "\n${BLUE}$1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${YELLOW}ℹ️  $1${NC}"
}

# Step 1: Customer starts a conversation
print_step "📝 Step 1: Customer starts a conversation"
CONVERSATION_RESPONSE=$(curl -s -X POST "$BASE_URL/api/conversations/" \
  -H "Content-Type: application/json" \
  -d '{"user_id": "customer-xyz-789"}')

CONVERSATION_ID=$(echo $CONVERSATION_RESPONSE | jq -r '.id')
if [ "$CONVERSATION_ID" != "null" ]; then
    print_success "Conversation created with ID: $CONVERSATION_ID"
    print_info "Status: $(echo $CONVERSATION_RESPONSE | jq -r '.status')"
    print_info "Assigned Agent: $(echo $CONVERSATION_RESPONSE | jq -r '.assigned_agent_id')"
else
    print_error "Failed to create conversation"
    echo $CONVERSATION_RESPONSE
    exit 1
fi

# Step 2: Admin lists all conversations
print_step "📋 Step 2: Admin lists all conversations"
CONVERSATIONS=$(curl -s -X GET "$BASE_URL/api/conversations/" \
  -H "X-User-Role: admin")

CONV_COUNT=$(echo $CONVERSATIONS | jq '. | length')
print_success "Found $CONV_COUNT conversations"
echo $CONVERSATIONS | jq '.[] | "   - ID: \(.id), User: \(.user_id), Status: \(.status)"' -r

# Step 3: Admin lists available agents
print_step "👥 Step 3: Admin lists available agents"
AGENTS=$(curl -s -X GET "$BASE_URL/api/users/agents" \
  -H "X-User-Role: admin")

AGENT_COUNT=$(echo $AGENTS | jq '. | length')
AGENT_ID=$(echo $AGENTS | jq -r '.[0].id')
print_success "Found $AGENT_COUNT agents"
echo $AGENTS | jq '.[] | "   - ID: \(.id), Name: \(.full_name), Email: \(.email)"' -r

# Step 4: Admin assigns conversation to agent
print_step "🔗 Step 4: Admin assigns conversation $CONVERSATION_ID to agent $AGENT_ID"
ASSIGNMENT_RESPONSE=$(curl -s -X POST "$BASE_URL/api/conversations/$CONVERSATION_ID/assign/$AGENT_ID" \
  -H "X-User-Role: admin")

ASSIGNED_AGENT=$(echo $ASSIGNMENT_RESPONSE | jq -r '.assigned_agent_id')
if [ "$ASSIGNED_AGENT" == "$AGENT_ID" ]; then
    print_success "Agent assigned successfully"
    print_info "Status: $(echo $ASSIGNMENT_RESPONSE | jq -r '.status')"
    print_info "Assigned Agent ID: $ASSIGNED_AGENT"
else
    print_error "Failed to assign agent"
    echo $ASSIGNMENT_RESPONSE
    exit 1
fi

# Step 5: Verify the assignment
print_step "🔍 Step 5: Verify the assignment"
VERIFICATION=$(curl -s -X GET "$BASE_URL/api/conversations/$CONVERSATION_ID")

VERIFIED_AGENT=$(echo $VERIFICATION | jq -r '.assigned_agent_id')
VERIFIED_STATUS=$(echo $VERIFICATION | jq -r '.status')
print_success "Assignment verified"
print_info "Status: $VERIFIED_STATUS"
print_info "Assigned Agent ID: $VERIFIED_AGENT"

# Step 6: Agent views their conversations
print_step "👤 Step 6: Agent views their conversations"
AGENT_CONVERSATIONS=$(curl -s -X GET "$BASE_URL/api/conversations/agent/$AGENT_ID" \
  -H "X-User-Role: agent" \
  -H "X-User-Id: $AGENT_ID")

AGENT_CONV_COUNT=$(echo $AGENT_CONVERSATIONS | jq '. | length')
print_success "Agent has $AGENT_CONV_COUNT assigned conversations"
echo $AGENT_CONVERSATIONS | jq '.[] | "   - ID: \(.id), User: \(.user_id), Status: \(.status)"' -r

# Step 7: Admin views unassigned conversations
print_step "📊 Step 7: Admin views unassigned conversations"
UNASSIGNED=$(curl -s -X GET "$BASE_URL/api/conversations/unassigned" \
  -H "X-User-Role: admin")

UNASSIGNED_COUNT=$(echo $UNASSIGNED | jq '. | length')
print_success "Found $UNASSIGNED_COUNT unassigned conversations"

# Step 8: Test permissions - Agent tries to access admin endpoint
print_step "🔒 Step 8: Test permissions - Agent tries to access admin endpoint"
PERMISSION_TEST=$(curl -s -X GET "$BASE_URL/api/users/" \
  -H "X-User-Role: agent" \
  -H "X-User-Id: $AGENT_ID")

if echo $PERMISSION_TEST | grep -q "Only admins can view all users"; then
    print_success "Permission check passed - Agent correctly denied access"
else
    print_error "Permission check failed - Agent should not have access"
    echo $PERMISSION_TEST
fi

# Step 9: Create a new agent (Admin only)
print_step "👤 Step 9: Admin creates a new agent"
NEW_AGENT=$(curl -s -X POST "$BASE_URL/api/users/" \
  -H "Content-Type: application/json" \
  -H "X-User-Role: admin" \
  -d '{
    "email": "<EMAIL>",
    "password": "agent3password",
    "full_name": "Agent Three",
    "role": "agent"
  }')

NEW_AGENT_ID=$(echo $NEW_AGENT | jq -r '.id')
if [ "$NEW_AGENT_ID" != "null" ]; then
    print_success "New agent created with ID: $NEW_AGENT_ID"
    print_info "Name: $(echo $NEW_AGENT | jq -r '.full_name')"
    print_info "Email: $(echo $NEW_AGENT | jq -r '.email')"
else
    print_info "Agent might already exist or creation failed"
fi

# Step 10: Test API documentation endpoints
print_step "📖 Step 10: Test API documentation endpoints"
API_INFO=$(curl -s -X GET "$BASE_URL/api")
APP_NAME=$(echo $API_INFO | jq -r '.app_name')
print_success "API info endpoint working"
print_info "App: $APP_NAME"

# Final summary
echo ""
echo "=================================================="
print_success "🎉 ALL TESTS PASSED!"
echo ""
echo -e "${GREEN}✅ Features Implemented:${NC}"
echo "   🔐 User Roles (Admin/Agent)"
echo "   📝 Conversation Assignment"
echo "   🛡️  Permission System"
echo "   📊 Admin Dashboard Endpoints"
echo "   👤 Agent Conversation Management"
echo "   🔗 Scalar API Documentation"
echo ""
echo -e "${BLUE}🚀 Ready for Production Use!${NC}"
echo ""
echo -e "${YELLOW}📚 API Documentation:${NC}"
echo "   📖 Swagger UI: $BASE_URL/docs"
echo "   📘 ReDoc: $BASE_URL/redoc"
echo "   ⚡ Scalar: $BASE_URL/scalar"
echo ""
echo -e "${YELLOW}🧪 Test the WebSocket:${NC}"
echo "   Connect as user: ws://localhost:8000/api/ws/chat/$CONVERSATION_ID?user_type=user&user_id=customer-xyz-789"
echo "   Connect as agent: ws://localhost:8000/api/ws/chat/$CONVERSATION_ID?user_type=agent&user_id=$AGENT_ID"
