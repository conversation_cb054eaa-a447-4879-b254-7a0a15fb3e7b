@echo off
setlocal enabledelayedexpansion

REM Yupcha Customer Bot AI - Development Startup Script (Windows)
REM This script starts both the backend and frontend development servers

echo 🚀 Starting Yupcha Customer Bot AI Development Environment
echo ============================================================

REM Check if uv is available
where uv >nul 2>nul
if %errorlevel% neq 0 (
    echo ❌ Error: 'uv' command not found. Please install uv first.
    echo    Visit: https://docs.astral.sh/uv/getting-started/installation/
    pause
    exit /b 1
)

REM Check if npm is available
where npm >nul 2>nul
if %errorlevel% neq 0 (
    echo ❌ Error: 'npm' command not found. Please install Node.js and npm first.
    echo    Visit: https://nodejs.org/
    pause
    exit /b 1
)

REM Start backend server
echo 🔧 Starting backend server...
echo    Command: uv run uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
start "Backend Server" cmd /k "uv run uvicorn app.main:app --reload --host 0.0.0.0 --port 8000"

REM Wait a moment for backend to start
timeout /t 3 /nobreak >nul

echo ✅ Backend server started
echo    URL: http://0.0.0.0:8000
echo    API Docs: http://0.0.0.0:8000/docs

REM Start frontend development server
echo.
echo 🎨 Starting frontend development server...
echo    Command: cd frontend-vue ^&^& npm run dev

REM Check if node_modules exists, if not run npm install
if not exist "frontend-vue\node_modules" (
    echo 📦 Installing frontend dependencies...
    cd frontend-vue
    npm install
    cd ..
)

start "Frontend Server" cmd /k "cd frontend-vue && npm run dev"

REM Wait a moment for frontend to start
timeout /t 3 /nobreak >nul

echo ✅ Frontend server started
echo    URL: http://localhost:5173
echo    Vue DevTools: http://localhost:5173/__devtools__/

echo.
echo 🎉 Development environment is ready!
echo ============================================================
echo 📱 Frontend:  http://localhost:5173
echo 🔧 Backend:   http://0.0.0.0:8000
echo 📚 API Docs:  http://0.0.0.0:8000/docs
echo 🛠️  DevTools:  http://localhost:5173/__devtools__/
echo.
echo Demo Credentials:
echo   Admin: <EMAIL> / adminpassword
echo   Agent: <EMAIL> / agentpassword
echo.
echo Both servers are running in separate windows.
echo Close the terminal windows to stop the servers.
echo ============================================================

pause
