#!/bin/bash

# 🗄️ Quick MinIO Setup Script for Yupcha Customer Bot AI

echo "🚀 Starting MinIO for Yupcha Customer Bot AI..."

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker first."
    exit 1
fi

# Stop existing MinIO container if running
echo "🧹 Cleaning up existing MinIO container..."
docker stop yupcha-minio 2>/dev/null || true
docker rm yupcha-minio 2>/dev/null || true

# Start MinIO container
echo "🏗️ Starting MinIO container..."
docker run -d \
  --name yupcha-minio \
  -p 9000:9000 \
  -p 9001:9001 \
  -e MINIO_ROOT_USER=minioadmin \
  -e MINIO_ROOT_PASSWORD=minioadmin \
  -v yupcha_minio_data:/data \
  minio/minio server /data --console-address ":9001"

# Wait for Min<PERSON> to start
echo "⏳ Waiting for MinIO to start..."
sleep 5

# Check if MinIO is running
if curl -s http://localhost:9000/minio/health/live > /dev/null; then
    echo "✅ MinIO is running successfully!"
    echo ""
    echo "📋 MinIO Information:"
    echo "   API Endpoint: http://localhost:9000"
    echo "   Console: http://localhost:9001"
    echo "   Username: minioadmin"
    echo "   Password: minioadmin"
    echo ""
    echo "🎯 Next Steps:"
    echo "   1. Restart your FastAPI server"
    echo "   2. Test file upload at http://localhost:8000/docs"
    echo "   3. Check uploaded files at http://localhost:9001"
else
    echo "❌ MinIO failed to start. Check Docker logs:"
    echo "   docker logs yupcha-minio"
fi
