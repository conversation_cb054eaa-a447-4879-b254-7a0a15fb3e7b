-- Direct SQL Fix for Customer Table
-- Run this SQL directly on the database to fix the schema

-- Check if organization_id column exists
DO $$
BEGIN
    -- Add organization_id column if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'customers' AND column_name = 'organization_id'
    ) THEN
        ALTER TABLE customers 
        ADD COLUMN organization_id INTEGER REFERENCES organizations(id);
        
        -- Set default organization for existing customers
        UPDATE customers 
        SET organization_id = (SELECT id FROM organizations ORDER BY id LIMIT 1)
        WHERE organization_id IS NULL;
        
        RAISE NOTICE 'organization_id column added to customers table';
    ELSE
        RAISE NOTICE 'organization_id column already exists in customers table';
    END IF;
END $$;

-- Create some test customers if none exist
INSERT INTO customers (customer_id, name, email, phone, location, organization_id, is_active)
SELECT 'cust_001', 'John <PERSON>e', '<EMAIL>', '******-0001', 'New York, NY', 
       (SELECT id FROM organizations ORDER BY id LIMIT 1), true
WHERE NOT EXISTS (SELECT 1 FROM customers WHERE customer_id = 'cust_001');

INSERT INTO customers (customer_id, name, email, phone, location, organization_id, is_active)
SELECT 'cust_002', 'Jane Smith', '<EMAIL>', '******-0002', 'Los Angeles, CA', 
       (SELECT id FROM organizations ORDER BY id LIMIT 1), true
WHERE NOT EXISTS (SELECT 1 FROM customers WHERE customer_id = 'cust_002');

INSERT INTO customers (customer_id, name, email, phone, location, organization_id, is_active)
SELECT 'cust_003', 'Mike Wilson', '<EMAIL>', '******-0003', 'Chicago, IL', 
       (SELECT id FROM organizations ORDER BY id LIMIT 1), true
WHERE NOT EXISTS (SELECT 1 FROM customers WHERE customer_id = 'cust_003');

-- Create test conversations for the customers
INSERT INTO conversations (customer_id, organization_id, status)
SELECT c.id, c.organization_id, 'active'
FROM customers c
WHERE c.customer_id IN ('cust_001', 'cust_002', 'cust_003')
AND NOT EXISTS (
    SELECT 1 FROM conversations conv WHERE conv.customer_id = c.id
);

-- Show results
SELECT 'Database Fix Results:' as message;
SELECT COUNT(*) as total_customers FROM customers;
SELECT COUNT(*) as total_conversations FROM conversations;
SELECT COUNT(*) as total_organizations FROM organizations;
