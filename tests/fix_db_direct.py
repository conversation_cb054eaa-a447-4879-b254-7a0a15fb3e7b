#!/usr/bin/env python3
"""
Direct Database Fix Script
Uses psycopg2 to directly fix the database schema.
"""

import os
import psycopg2
from urllib.parse import urlparse

# Database URL from environment or hardcoded
DATABASE_URL = "*******************************************************************************************************************"

def fix_database():
    """Fix database schema issues"""
    try:
        # Parse the database URL
        parsed = urlparse(DATABASE_URL)
        
        # Connect to the database
        conn = psycopg2.connect(
            host=parsed.hostname,
            port=parsed.port,
            database=parsed.path[1:],  # Remove leading slash
            user=parsed.username,
            password=parsed.password
        )
        
        cur = conn.cursor()
        
        print("🔍 Checking database schema...")
        
        # Check if organization_id column exists in customers table
        cur.execute("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = 'customers' AND column_name = 'organization_id'
        """)
        
        columns = cur.fetchall()
        
        if columns:
            print("✅ organization_id column already exists in customers table!")
        else:
            print("➕ Adding organization_id column to customers table...")
            
            # Add the organization_id column
            cur.execute("""
                ALTER TABLE customers 
                ADD COLUMN organization_id INTEGER REFERENCES organizations(id)
            """)
            
            conn.commit()
            print("✅ organization_id column added successfully!")
            
            # Set default organization for existing customers
            print("🔧 Setting default organization for existing customers...")
            
            # Get the first organization ID
            cur.execute("SELECT id FROM organizations ORDER BY id LIMIT 1")
            org_row = cur.fetchone()
            
            if org_row:
                default_org_id = org_row[0]
                cur.execute("""
                    UPDATE customers 
                    SET organization_id = %s 
                    WHERE organization_id IS NULL
                """, (default_org_id,))
                
                conn.commit()
                print(f"✅ Set default organization_id = {default_org_id} for existing customers")
            else:
                print("⚠️ No organizations found. Please create an organization first.")
        
        # Now check customers
        print("\n📊 Checking customers...")
        cur.execute("""
            SELECT id, customer_id, name, organization_id 
            FROM customers 
            ORDER BY id 
            LIMIT 5
        """)
        
        customers = cur.fetchall()
        print(f"Current customers ({len(customers)}):")
        for customer in customers:
            print(f"  - ID: {customer[0]}, Customer ID: {customer[1]}, Name: {customer[2]}, Org: {customer[3]}")
        
        # Check conversations
        print("\n💬 Checking conversations...")
        cur.execute("""
            SELECT id, customer_id, organization_id, status 
            FROM conversations 
            ORDER BY id 
            LIMIT 5
        """)
        
        conversations = cur.fetchall()
        print(f"Current conversations ({len(conversations)}):")
        for conv in conversations:
            print(f"  - ID: {conv[0]}, Customer: {conv[1]}, Org: {conv[2]}, Status: {conv[3]}")
        
        cur.close()
        conn.close()
        
        print("\n✅ Database fix completed successfully!")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        raise

if __name__ == "__main__":
    fix_database()
