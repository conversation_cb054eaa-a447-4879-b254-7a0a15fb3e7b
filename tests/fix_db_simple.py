#!/usr/bin/env python3
"""
Simple Database Fix Script
Uses SQLAlchemy to add missing column.
"""

import asyncio
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text
from app.db.session import AsyncSessionLocal

async def fix_database():
    """Fix database schema issues"""
    async with AsyncSessionLocal() as db:
        try:
            print("🔍 Checking database schema...")
            
            # Check if organization_id column exists in customers table
            result = await db.execute(text("""
                SELECT column_name 
                FROM information_schema.columns 
                WHERE table_name = 'customers' AND column_name = 'organization_id'
            """))
            
            columns = result.fetchall()
            
            if columns:
                print("✅ organization_id column already exists in customers table!")
            else:
                print("➕ Adding organization_id column to customers table...")
                
                # Add the organization_id column
                await db.execute(text("""
                    ALTER TABLE customers 
                    ADD COLUMN organization_id INTEGER REFERENCES organizations(id)
                """))
                
                await db.commit()
                print("✅ organization_id column added successfully!")
                
                # Set default organization for existing customers
                print("🔧 Setting default organization for existing customers...")
                
                # Get the first organization ID
                org_result = await db.execute(text("SELECT id FROM organizations ORDER BY id LIMIT 1"))
                org_row = org_result.fetchone()
                
                if org_row:
                    default_org_id = org_row[0]
                    await db.execute(text("""
                        UPDATE customers 
                        SET organization_id = :org_id 
                        WHERE organization_id IS NULL
                    """), {"org_id": default_org_id})
                    
                    await db.commit()
                    print(f"✅ Set default organization_id = {default_org_id} for existing customers")
                else:
                    print("⚠️ No organizations found. Please create an organization first.")
            
            # Now check customers
            print("\n📊 Checking customers...")
            customers_result = await db.execute(text("""
                SELECT id, customer_id, name, organization_id 
                FROM customers 
                ORDER BY id 
                LIMIT 5
            """))
            
            customers = customers_result.fetchall()
            print(f"Current customers ({len(customers)}):")
            for customer in customers:
                print(f"  - ID: {customer[0]}, Customer ID: {customer[1]}, Name: {customer[2]}, Org: {customer[3]}")
            
            print("\n✅ Database fix completed successfully!")
            
        except Exception as e:
            print(f"❌ Error: {e}")
            await db.rollback()
            raise

if __name__ == "__main__":
    asyncio.run(fix_database())
