#!/usr/bin/env python3
"""
Run SQL Fix Script
Executes the direct SQL fix for the database.
"""

import subprocess
import os

# Database connection details
DB_HOST = "**************"
DB_PORT = "4675"
DB_NAME = "postgres"
DB_USER = "postgres"
DB_PASSWORD = "GKq4oJsg86DuGW0fxlgpmmWoHjykyRJuEWjVPv2QIS8OMASsnxhe3wwSynTuffJP"

def run_sql_fix():
    """Run the SQL fix script"""
    try:
        print("🔧 Running SQL fix script...")
        
        # Set environment variable for password
        env = os.environ.copy()
        env['PGPASSWORD'] = DB_PASSWORD
        
        # Run psql command
        cmd = [
            'psql',
            '-h', DB_HOST,
            '-p', DB_PORT,
            '-U', DB_USER,
            '-d', DB_NAME,
            '-f', 'direct_sql_fix.sql'
        ]
        
        result = subprocess.run(cmd, env=env, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ SQL fix completed successfully!")
            print("Output:")
            print(result.stdout)
        else:
            print("❌ SQL fix failed!")
            print("Error:")
            print(result.stderr)
            
    except FileNotFoundError:
        print("❌ psql command not found. Please install PostgreSQL client.")
        print("On Ubuntu/Debian: sudo apt-get install postgresql-client")
        print("On macOS: brew install postgresql")
    except Exception as e:
        print(f"❌ Error running SQL fix: {e}")

if __name__ == "__main__":
    run_sql_fix()
