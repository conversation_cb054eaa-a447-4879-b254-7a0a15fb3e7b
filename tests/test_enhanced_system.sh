#!/bin/bash

# Enhanced System Test with Phase 2 Features
# Tests the new auto-assignment and notification features

BASE_URL="http://localhost:8000"

echo "🚀 Yupcha Customer Bot AI - Enhanced System Test"
echo "=================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_step() {
    echo -e "\n${BLUE}$1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${YELLOW}ℹ️  $1${NC}"
}

# Step 1: Admin login
print_step "🔐 Step 1: Admin login"
LOGIN_RESPONSE=$(curl -s -X POST "$BASE_URL/api/auth/login" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "username=<EMAIL>&password=adminpassword" \
  -c cookies.txt)

if echo $LOGIN_RESPONSE | grep -q "Login successful"; then
    print_success "Admin login successful"
else
    print_error "Admin login failed"
    echo $LOGIN_RESPONSE
    exit 1
fi

# Step 2: Create organization with default team
print_step "🏢 Step 2: Create organization"
ORG_DATA='{
  "name": "Enhanced Test Org",
  "description": "Organization with auto-assignment",
  "email": "<EMAIL>"
}'

ORG_RESPONSE=$(curl -s -X POST "$BASE_URL/api/organizations/" \
  -H "Content-Type: application/json" \
  -b cookies.txt \
  -d "$ORG_DATA")

ORG_ID=$(echo $ORG_RESPONSE | jq -r '.id')
print_success "Organization created with ID: $ORG_ID"

# Step 3: Create a team for this organization
print_step "👥 Step 3: Create support team"
TEAM_DATA="{
  \"name\": \"Auto-Assignment Team\",
  \"description\": \"Team for automatic conversation assignment\",
  \"organization_id\": $ORG_ID
}"

TEAM_RESPONSE=$(curl -s -X POST "$BASE_URL/api/teams/" \
  -H "Content-Type: application/json" \
  -b cookies.txt \
  -d "$TEAM_DATA")

TEAM_ID=$(echo $TEAM_RESPONSE | jq -r '.id')
print_success "Team created with ID: $TEAM_ID"

# Step 4: Set the team as default for the organization
print_step "🔗 Step 4: Set default team for organization"
ORG_UPDATE="{
  \"default_team_id\": $TEAM_ID
}"

UPDATE_RESPONSE=$(curl -s -X PUT "$BASE_URL/api/organizations/$ORG_ID" \
  -H "Content-Type: application/json" \
  -b cookies.txt \
  -d "$ORG_UPDATE")

print_success "Default team set for organization"

# Step 5: Create a customer
print_step "👤 Step 5: Create customer"
CUSTOMER_DATA='{
  "customer_id": "auto-cust-001",
  "name": "Auto Customer",
  "email": "<EMAIL>",
  "ip_address": "********",
  "location": "Auto City"
}'

CUSTOMER_RESPONSE=$(curl -s -X POST "$BASE_URL/api/customers/" \
  -H "Content-Type: application/json" \
  -d "$CUSTOMER_DATA")

CUSTOMER_DB_ID=$(echo $CUSTOMER_RESPONSE | jq -r '.id')
print_success "Customer created with ID: $CUSTOMER_DB_ID"

# Step 6: Create conversation (should auto-assign to default team)
print_step "💬 Step 6: Create conversation (auto-assignment test)"
CONV_DATA="{
  \"customer_id\": $CUSTOMER_DB_ID,
  \"organization_id\": $ORG_ID
}"

CONV_RESPONSE=$(curl -s -X POST "$BASE_URL/api/conversations/" \
  -H "Content-Type: application/json" \
  -d "$CONV_DATA")

CONV_ID=$(echo $CONV_RESPONSE | jq -r '.id')
ASSIGNED_TEAM=$(echo $CONV_RESPONSE | jq -r '.assigned_team_id')
STATUS=$(echo $CONV_RESPONSE | jq -r '.status')

if [ "$ASSIGNED_TEAM" == "$TEAM_ID" ]; then
    print_success "✨ Auto-assignment worked! Conversation $CONV_ID assigned to team $TEAM_ID"
    print_info "Status: $STATUS"
else
    print_error "Auto-assignment failed. Expected team $TEAM_ID, got $ASSIGNED_TEAM"
fi

# Step 7: Test WebSocket with new authentication
print_step "🔌 Step 7: Test WebSocket authentication"
print_info "WebSocket endpoints available:"
echo "   Customer: ws://localhost:8000/api/ws/chat/$CONV_ID?customer_id=auto-cust-001"
echo "   Agent: ws://localhost:8000/api/ws/chat/$CONV_ID (with session cookie)"

# Step 8: Create an agent for the team
print_step "👨‍💼 Step 8: Create agent for the team"
AGENT_DATA="{
  \"email\": \"<EMAIL>\",
  \"password\": \"agentpass123\",
  \"full_name\": \"Auto Agent\",
  \"role\": \"agent\",
  \"organization_id\": $ORG_ID,
  \"team_id\": $TEAM_ID
}"

AGENT_RESPONSE=$(curl -s -X POST "$BASE_URL/api/users/" \
  -H "Content-Type: application/json" \
  -b cookies.txt \
  -d "$AGENT_DATA")

AGENT_ID=$(echo $AGENT_RESPONSE | jq -r '.id')
if [ "$AGENT_ID" != "null" ]; then
    print_success "Agent created with ID: $AGENT_ID"
else
    print_info "Agent creation might have failed or already exists"
fi

# Step 9: Agent login and view team conversations
print_step "🔐 Step 9: Agent login and view team conversations"
AGENT_LOGIN=$(curl -s -X POST "$BASE_URL/api/auth/login" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "username=<EMAIL>&password=agentpass123" \
  -c agent_cookies.txt)

if echo $AGENT_LOGIN | grep -q "Login successful"; then
    print_success "Agent login successful"
    
    # Agent views team conversations
    TEAM_CONVS=$(curl -s -X GET "$BASE_URL/api/conversations/team/$TEAM_ID" \
      -b agent_cookies.txt)
    
    CONV_COUNT=$(echo $TEAM_CONVS | jq '. | length')
    print_success "Agent can see $CONV_COUNT team conversations"
else
    print_error "Agent login failed"
fi

# Step 10: Test enhanced API documentation
print_step "📚 Step 10: Test enhanced API documentation"
print_success "API Documentation available at:"
echo "   📖 Swagger UI: $BASE_URL/docs"
echo "   📘 ReDoc: $BASE_URL/redoc"
echo "   ⚡ Scalar: $BASE_URL/scalar"

# Cleanup
rm -f cookies.txt agent_cookies.txt

# Final summary
echo ""
echo "=================================================="
print_success "🎉 ENHANCED SYSTEM TEST COMPLETED!"
echo ""
echo -e "${GREEN}✅ Phase 2 Features Implemented:${NC}"
echo "   🔄 Auto-assignment to default teams"
echo "   📧 Background task notifications"
echo "   🔐 Enhanced WebSocket authentication"
echo "   🏢 Organization-team relationships"
echo "   👥 Team-based conversation management"
echo "   📊 Enhanced CRUD operations"
echo ""
echo -e "${BLUE}🚀 System Ready for Chatwoot-level Features!${NC}"
echo ""
echo -e "${YELLOW}🔧 Next Phase Recommendations:${NC}"
echo "   🎛️  Admin Dashboard (fastapi-admin)"
echo "   📱 Real-time notifications"
echo "   🤖 Advanced bot responses"
echo "   📈 Analytics and reporting"
echo "   🔍 Search and filtering"
echo "   📎 File attachments"
