#!/usr/bin/env python3
"""
Fix Database via API Script
Uses the admin API endpoints to fix database issues.
"""

import requests
import json

BASE_URL = "http://localhost:8000/api"

def login_admin():
    """Login as admin and get token"""
    login_data = {
        "username": "<EMAIL>",
        "password": "adminpassword"
    }
    
    response = requests.post(f"{BASE_URL}/auth/login", data=login_data)
    if response.status_code == 200:
        token = response.json().get("access_token")
        print("✅ Admin login successful!")
        return token
    else:
        print(f"❌ Login failed: {response.status_code} - {response.text}")
        return None

def fix_database(token):
    """Fix database schema"""
    headers = {"Authorization": f"Bearer {token}"}
    
    print("🔧 Fixing database schema...")
    response = requests.post(f"{BASE_URL}/admin/fix-database", headers=headers)
    
    if response.status_code == 200:
        result = response.json()
        print(f"✅ Database fix result: {result}")
        return True
    else:
        print(f"❌ Database fix failed: {response.status_code} - {response.text}")
        return False

def check_database_status(token):
    """Check database status"""
    headers = {"Authorization": f"Bearer {token}"}
    
    print("📊 Checking database status...")
    response = requests.get(f"{BASE_URL}/admin/database-status", headers=headers)
    
    if response.status_code == 200:
        status = response.json()
        print("Database Status:")
        for key, value in status.items():
            print(f"  - {key}: {value}")
        return status
    else:
        print(f"❌ Status check failed: {response.status_code} - {response.text}")
        return None

def create_test_data(token):
    """Create test customers and conversations"""
    headers = {"Authorization": f"Bearer {token}"}
    
    print("🧪 Creating test data...")
    response = requests.post(f"{BASE_URL}/admin/create-test-data", headers=headers)
    
    if response.status_code == 200:
        result = response.json()
        print(f"✅ Test data created: {result}")
        return True
    else:
        print(f"❌ Test data creation failed: {response.status_code} - {response.text}")
        return False

def test_customers_endpoint(token):
    """Test if customers endpoint works now"""
    headers = {"Authorization": f"Bearer {token}"}
    
    print("🛒 Testing customers endpoint...")
    response = requests.get(f"{BASE_URL}/customers/", headers=headers)
    
    if response.status_code == 200:
        customers = response.json()
        print(f"✅ Customers endpoint working! Found {len(customers)} customers")
        for customer in customers[:3]:  # Show first 3
            print(f"  - {customer.get('name')} ({customer.get('customer_id')})")
        return True
    else:
        print(f"❌ Customers endpoint failed: {response.status_code} - {response.text}")
        return False

def main():
    """Main function to fix all database issues"""
    print("🚀 Starting database fix process...")
    
    # Step 1: Login
    token = login_admin()
    if not token:
        return
    
    # Step 2: Check current status
    status = check_database_status(token)
    
    # Step 3: Fix database if needed
    if status and not status.get("customers_has_org_id", False):
        print("\n🔧 organization_id column missing, fixing...")
        fix_database(token)
    else:
        print("\n✅ Database schema looks good!")
    
    # Step 4: Create test data if no customers exist
    if status and status.get("customers_count", 0) == 0:
        print("\n🧪 No customers found, creating test data...")
        create_test_data(token)
    else:
        print(f"\n📊 Found {status.get('customers_count', 0)} existing customers")
    
    # Step 5: Test customers endpoint
    print("\n🧪 Testing customers endpoint...")
    test_customers_endpoint(token)
    
    print("\n✅ Database fix process completed!")

if __name__ == "__main__":
    main()
