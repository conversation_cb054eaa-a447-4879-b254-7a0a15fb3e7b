<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Yupcha Full System WebSocket Demo</title>
    <style>
        :root {
            --blue: #0d6efd; --green: #198754; --yellow: #ffc107; --red: #dc3545;
            --gray-100: #f8f9fa; --gray-200: #e9ecef; --gray-500: #adb5bd; --gray-700: #495057;
        }
        body { font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif; margin: 0; padding: 20px; background-color: #f0f2f5; color: var(--gray-700); }
        .grid-container { display: grid; grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)); gap: 20px; max-width: 1800px; margin: auto; }
        .panel { background: white; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.08); padding: 20px; display: flex; flex-direction: column; }
        .setup-panel { grid-column: 1 / -1; margin-bottom: 20px; }
        .chat-panel, .notifications-panel { min-height: 70vh; }
        h2, h3 { color: #333; border-bottom: 1px solid #eee; padding-bottom: 10px; margin-top: 0; }
        .customer-panel { border-top: 4px solid var(--blue); }
        .agent-panel { border-top: 4px solid var(--green); }
        .notifications-panel { border-top: 4px solid var(--yellow); }
        .messages { flex: 1; overflow-y: auto; border: 1px solid #ddd; padding: 10px; margin-bottom: 15px; background-color: #fdfdfd; border-radius: 4px; display: flex; flex-direction: column; }
        .message { display: flex; flex-direction: column; margin-bottom: 12px; max-width: 85%; padding: 8px 12px; border-radius: 12px; line-height: 1.4; position: relative; }
        .message .sender-info { font-size: 0.75rem; font-weight: bold; margin-bottom: 4px; }
        .message.sent { background-color: var(--blue); color: white; align-self: flex-end; border-bottom-right-radius: 2px; }
        .message.received { background-color: var(--gray-200); color: #212529; align-self: flex-start; border-bottom-left-radius: 2px; }
        .message.agent .content, .message.admin .content { background-color: var(--green); color: white; }
        .message.system { background-color: var(--gray-500); color: white; font-style: italic; font-size: 0.8rem; text-align: center; align-self: center; }
        .message.notification { background-color: #fff3cd; color: #664d03; font-style: italic; font-size: 0.8rem; align-self: center; white-space: pre-wrap; }
        .message .actions { position: absolute; top: 2px; right: 8px; font-size: 0.7rem; }
        .message .actions button { background: none; border: none; cursor: pointer; padding: 2px; opacity: 0.6; }
        .message .actions button:hover { opacity: 1; }
        .message.deleted .content { text-decoration: line-through; color: #999; }
        .input-group { display: flex; gap: 10px; margin-top: auto; }
        .input-group input[type="text"] { flex: 1; padding: 10px; border: 1px solid #ddd; border-radius: 4px; }
        .input-group input[type="file"] { border: none; }
        .input-group button { padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; color: white; }
        .status { font-weight: bold; margin-bottom: 10px; }
        .connected { color: var(--green); } .disconnected { color: var(--red); }
        .setup-controls, .login-controls { display: flex; flex-wrap: wrap; gap: 10px; align-items: center; }
        .setup-controls input { padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        .setup-controls button, .login-controls button { background-color: var(--gray-700); }
        .setup-controls button.primary { background-color: var(--blue); }
    </style>
</head>
<body>
    <div class="setup-panel panel">
        <h2>🚀 Yupcha Full System WebSocket Demo</h2>
        <div class="setup-controls">
            <label>Conv ID: <input type="text" id="conversationId" placeholder="Auto-filled" readonly></label>
            <label>Customer ID: <input type="text" id="customerId" placeholder="Auto-filled" readonly></label>
            <button class="primary" onclick="createTestDataAndConnect()">▶️ Setup New Demo & Connect</button>
            <button onclick="disconnectAll()">⏹️ Disconnect All</button>
        </div>
    </div>

    <div class="grid-container">
        <!-- Customer Panel -->
        <div class="chat-panel customer-panel">
            <h3>👤 Customer Chat</h3>
            <div class="status disconnected" id="customerStatus">Disconnected</div>
            <div class="messages" id="customerMessages"></div>
            <div class="input-group">
                <input type="text" id="customerInput" placeholder="Type or drop a file...">
                <button onclick="sendCustomerMessage()">Send</button>
                <input type="file" id="customerFileInput" onchange="sendMediaMessage('customer')">
            </div>
        </div>

        <!-- Agent Panel -->
        <div class="chat-panel agent-panel">
            <h3>👨‍💼 Agent / Admin Chat</h3>
            <div class="status disconnected" id="agentStatus">Not Logged In</div>
            <div class="login-controls">
                <button onclick="loginAndConnect('agent')">Login as Agent</button>
                <button onclick="loginAndConnect('admin')">Login as Admin</button>
            </div>
            <div class="messages" id="agentMessages"></div>
            <div class="input-group">
                <input type="text" id="agentInput" placeholder="Type or drop a file...">
                <button onclick="sendAgentMessage()">Send</button>
                <input type="file" id="agentFileInput" onchange="sendMediaMessage('agent')">
            </div>
        </div>

        <!-- Notifications Panel -->
        <div class="notifications-panel panel">
            <h3>🔔 Real-time Agent Notifications</h3>
            <div class="status disconnected" id="notificationStatus">Disconnected</div>
            <div class="messages" id="notificationMessages"></div>
        </div>
    </div>

    <script>
        const API_BASE_URL = "http://localhost:8000";
        const WS_BASE_URL = "ws://localhost:8000";

        let customerWs, agentWs, notificationWs;
        let loggedInUser = {};

        // --- UI & Helper Functions ---
        function updateStatus(panel, text, isConnected) {
            const el = document.getElementById(`${panel}Status`);
            el.textContent = text;
            el.className = `status ${isConnected ? 'connected' : 'disconnected'}`;
        }

        function addMessage(panel, sender, content, msgId, assetUrl = null) {
            const messagesEl = document.getElementById(`${panel}Messages`);
            const msgEl = document.createElement('div');
            const senderClass = (loggedInUser.role && sender === loggedInUser.role) || (panel === 'customer' && sender === 'customer') ? 'sent' : 'received';
            msgEl.className = `message ${sender} ${senderClass}`;
            msgEl.id = `msg-${panel}-${msgId}`;
            
            let messageContent = `<div class="content">${content || ''}</div>`;
            if (assetUrl) {
                messageContent += `<div class="content"><img src="${assetUrl}" alt="attachment" style="max-width: 100%; border-radius: 8px;"></div>`;
            }

            msgEl.innerHTML = `
                <div class="sender-info">${sender}</div>
                ${messageContent}
                <div class="actions">
                    ${(loggedInUser.role && msgId) ? `<button onclick="deleteMessage(${msgId})">🗑️</button>` : ''}
                </div>`;
            messagesEl.appendChild(msgEl);
            messagesEl.scrollTop = messagesEl.scrollHeight;
        }
        
        function addSystemMessage(panel, text, type = 'system') {
             const messagesEl = document.getElementById(`${panel}Messages`);
             messagesEl.innerHTML += `<div class="message ${type}"><div class="content">${text}</div></div>`;
             messagesEl.scrollTop = messagesEl.scrollHeight;
        }

        function handleKey(event, sendFunction) {
            if (event.key === 'Enter') sendFunction();
        }
        
        // --- WebSocket Connection Logic ---
        async function loginAndConnect(role) {
            disconnectAll();
            const email = role === 'admin' ? '<EMAIL>' : '<EMAIL>';
            const password = role === 'admin' ? 'adminpassword' : 'agentpassword';
            
            try {
                const response = await fetch(`${API_BASE_URL}/api/auth/login`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                    body: `username=${email}&password=${password}`,
                });
                if (!response.ok) throw new Error('Login failed');
                await response.json();

                const meResponse = await fetch(`${API_BASE_URL}/api/auth/me`);
                loggedInUser = await meResponse.json();
                
                updateStatus('agent', `Logged in as ${loggedInUser.role} (ID: ${loggedInUser.id})`, true);
                connectAgentWebSocket();
                connectNotificationWebSocket();
            } catch (error) {
                updateStatus('agent', `Login Error: ${error.message}`, false);
            }
        }

        function setupWebSocket(panel, wsUrl) {
            const ws = new WebSocket(wsUrl);
            ws.onopen = () => {
                updateStatus(panel, 'Connected', true);
                addSystemMessage(panel, 'Connected to chat.');
            };
            ws.onmessage = (event) => {
                const data = JSON.parse(event.data);
                if (data.type === 'message') {
                    addMessage(panel, data.sender, data.content, data.id, data.asset_url);
                } else if (data.type === 'message_deleted') {
                    const msgEl = document.getElementById(`msg-${panel}-${data.message_id}`);
                    if (msgEl) msgEl.classList.add('deleted');
                } else {
                     addSystemMessage(panel, `System: ${data.detail || JSON.stringify(data)}`);
                }
            };
            ws.onclose = () => updateStatus(panel, 'Disconnected', false);
            ws.onerror = () => updateStatus(panel, 'Error', false);
            return ws;
        }

        const connectAgentWebSocket = () => {
            const convId = document.getElementById('conversationId').value;
            if (!convId) return;
            agentWs = setupWebSocket('agent', `${WS_BASE_URL}/api/ws/chat/${convId}`);
        };

        const connectCustomerWebSocket = () => {
            const convId = document.getElementById('conversationId').value;
            const customerId = document.getElementById('customerId').value;
            if (!convId || !customerId) return;
            customerWs = setupWebSocket('customer', `${WS_BASE_URL}/api/ws/chat/${convId}?customer_id=${customerId}`);
        };
        
        const connectNotificationWebSocket = () => {
            notificationWs = new WebSocket(`${WS_BASE_URL}/api/ws/agent-notifications`);
            notificationWs.onopen = () => updateStatus('notification', 'Connected', true);
            notificationWs.onmessage = (event) => {
                const data = JSON.parse(event.data);
                addSystemMessage('notification', `EVENT: ${data.type}\n${JSON.stringify(data.data, null, 2)}`, 'notification');
            };
            notificationWs.onclose = () => updateStatus('notification', 'Disconnected', false);
            notificationWs.onerror = () => updateStatus('notification', 'Error', false);
        };
        
        // --- Message & Media Logic ---
        function sendMessage(ws, inputId, senderType) {
            const input = document.getElementById(inputId);
            const content = input.value.trim();
            if (content && ws && ws.readyState === WebSocket.OPEN) {
                ws.send(JSON.stringify({ content: content, sender: senderType }));
                input.value = '';
            }
        }
        const sendCustomerMessage = () => sendMessage(customerWs, 'customerInput', 'customer');
        const sendAgentMessage = () => sendMessage(agentWs, 'agentInput', loggedInUser.role);

        async function sendMediaMessage(senderType) {
            const fileInput = document.getElementById(`${senderType}FileInput`);
            const file = fileInput.files[0];
            if (!file) return;

            const convId = document.getElementById('conversationId').value;
            const customerId = document.getElementById('customerId').value;
            const formData = new FormData();
            formData.append('file', file);

            let uploadUrl;
            if (senderType === 'customer') {
                formData.append('customer_id', customerId);
                formData.append('conversation_id', convId);
                uploadUrl = `${API_BASE_URL}/api/media/upload/public`;
            } else {
                uploadUrl = `${API_BASE_URL}/api/media/upload`;
            }

            try {
                const response = await fetch(uploadUrl, { method: 'POST', body: formData });
                if (!response.ok) throw new Error(await response.text());
                
                const asset = await response.json();
                const ws = senderType === 'customer' ? customerWs : agentWs;
                if (ws && ws.readyState === WebSocket.OPEN) {
                    ws.send(JSON.stringify({
                        content: `Attachment: ${file.name}`,
                        sender: senderType,
                        message_type: asset.file_type,
                        asset_id: asset.asset_id
                    }));
                }
            } catch (error) {
                alert(`Upload failed: ${error.message}`);
            } finally {
                fileInput.value = '';
            }
        }

        async function deleteMessage(messageId) {
            try {
                const response = await fetch(`${API_BASE_URL}/api/messages/${messageId}`, { method: 'DELETE' });
                if (!response.ok) throw new Error(await response.text());
            } catch (error) { alert(`Error deleting message: ${error.message}`); }
        }
        
        // --- Setup and Control ---
        function disconnectAll() {
            [customerWs, agentWs, notificationWs].forEach(ws => {
                if (ws && ws.readyState === WebSocket.OPEN) ws.close();
            });
            loggedInUser = {};
            updateStatus('agent', 'Not Logged In', false);
        }

        const createTestDataAndConnect = async () => {
            disconnectAll();
            addSystemMessage('customer', "Setting up new demo environment...", 'system');
            addSystemMessage('agent', "Setting up new demo environment...", 'system');
            
            try {
                // Login as admin to perform setup
                const loginRes = await fetch(`${API_BASE_URL}/api/auth/login`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                    body: 'username=<EMAIL>&password=adminpassword',
                });
                if (!loginRes.ok) throw new Error('Admin login failed');

                const custId = `demo-${Date.now()}`;
                const custRes = await fetch(`${API_BASE_URL}/api/customers/`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ customer_id: custId, name: 'Demo Customer' })
                });
                const customer = await custRes.json();
                
                const convRes = await fetch(`${API_BASE_URL}/api/conversations/`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ customer_id: customer.id, organization_id: 1 })
                });
                const conversation = await convRes.json();
                
                document.getElementById('conversationId').value = conversation.id;
                document.getElementById('customerId').value = custId;
                
                addSystemMessage('customer', `✅ Test data ready. Connecting...`, 'system');
                addSystemMessage('agent', `✅ Test data ready. Connecting...`, 'system');
                
                connectCustomerWebSocket();
                loginAndConnect('agent');
                
            } catch(e) {
                alert(`Error creating test data: ${e.message}`);
            }
        }

        function checkServer() {
            fetch(`${API_BASE_URL}/api`)
                .then(r => r.ok ? console.log('✅ Server is running!') : console.error('❌ Server responded with an error.'))
                .catch(() => alert('❌ Could not connect to server.'));
        }

        window.onload = checkServer;
    </script>
</body>
</html>