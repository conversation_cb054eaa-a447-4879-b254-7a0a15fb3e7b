#!/usr/bin/env python3
"""
Debug WebSocket connections to identify the issue
"""

import asyncio
import websockets
import json
import requests

async def test_customer_websocket():
    """Test customer WebSocket connection"""
    print("🧪 Testing Customer WebSocket Connection")
    print("-" * 40)
    
    conversation_id = 15
    customer_id = "demo-customer"
    
    ws_url = f"ws://localhost:8000/api/ws/chat/{conversation_id}?customer_id={customer_id}"
    print(f"🔗 Connecting to: {ws_url}")
    
    try:
        async with websockets.connect(ws_url) as websocket:
            print("✅ Customer WebSocket connected successfully!")
            
            # Wait for connection message
            message = await websocket.recv()
            print(f"📨 Received: {message}")
            
            # Send a test message
            test_message = {
                "content": "Hello from customer!",
                "type": "message"
            }
            await websocket.send(json.dumps(test_message))
            print(f"📤 Sent: {test_message}")
            
            # Wait for response (with timeout)
            try:
                response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                print(f"📨 Response: {response}")
            except asyncio.TimeoutError:
                print("⏰ No response received within 5 seconds (this is normal for customer messages)")
            
    except Exception as e:
        print(f"❌ Customer WebSocket failed: {e}")
        return False
    
    return True

async def test_agent_websocket():
    """Test agent WebSocket connection"""
    print("\n🧪 Testing Agent WebSocket Connection")
    print("-" * 40)
    
    # First login to get session cookie
    print("🔐 Logging in as agent...")
    login_response = requests.post(
        "http://localhost:8000/api/auth/login",
        data={"username": "<EMAIL>", "password": "agentpassword"}
    )
    
    if login_response.status_code != 200:
        print(f"❌ Agent login failed: {login_response.text}")
        return False
    
    print("✅ Agent login successful")
    
    # Get session cookie
    session_cookie = login_response.cookies.get('yupcha_session')
    print(f"🍪 Session cookie: {session_cookie[:20]}..." if session_cookie else "❌ No session cookie")
    
    conversation_id = 15
    ws_url = f"ws://localhost:8000/api/ws/chat/{conversation_id}"
    print(f"🔗 Connecting to: {ws_url}")
    
    # Prepare headers with cookie
    headers = {}
    if session_cookie:
        headers["Cookie"] = f"yupcha_session={session_cookie}"
    
    try:
        # Create connection with headers
        if headers:
            async with websockets.connect(ws_url, additional_headers=headers) as websocket:
                print("✅ Agent WebSocket connected successfully!")

                # Wait for connection message
                message = await websocket.recv()
                print(f"📨 Received: {message}")

                # Send a test message
                test_message = {
                    "content": "Hello from agent!",
                    "type": "message"
                }
                await websocket.send(json.dumps(test_message))
                print(f"📤 Sent: {test_message}")

                # Wait for response
                response = await websocket.recv()
                print(f"📨 Response: {response}")
        else:
            async with websockets.connect(ws_url) as websocket:
                print("✅ Agent WebSocket connected successfully!")

                # Wait for connection message
                message = await websocket.recv()
                print(f"📨 Received: {message}")

                # Send a test message
                test_message = {
                    "content": "Hello from agent!",
                    "type": "message"
                }
                await websocket.send(json.dumps(test_message))
                print(f"📤 Sent: {test_message}")

                # Wait for response
                response = await websocket.recv()
                print(f"📨 Response: {response}")
            
    except Exception as e:
        print(f"❌ Agent WebSocket failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

async def main():
    print("🚀 WebSocket Debug Test")
    print("=" * 50)
    
    # Test server connectivity first
    try:
        response = requests.get("http://localhost:8000/api", timeout=5)
        if response.status_code == 200:
            print("✅ Server is running and accessible")
        else:
            print(f"❌ Server returned status: {response.status_code}")
            return
    except Exception as e:
        print(f"❌ Cannot connect to server: {e}")
        return
    
    # Test customer WebSocket
    customer_success = await test_customer_websocket()
    
    # Test agent WebSocket
    agent_success = await test_agent_websocket()
    
    print("\n" + "=" * 50)
    print("🎯 Test Results:")
    print(f"   Customer WebSocket: {'✅ PASS' if customer_success else '❌ FAIL'}")
    print(f"   Agent WebSocket: {'✅ PASS' if agent_success else '❌ FAIL'}")
    
    if customer_success and agent_success:
        print("\n🎉 All WebSocket connections working!")
    else:
        print("\n🔧 Some connections failed - check the errors above")

if __name__ == "__main__":
    asyncio.run(main())
