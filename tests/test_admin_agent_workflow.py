#!/usr/bin/env python3
"""
Complete Admin/Agent workflow test script.
Tests the entire user roles and conversation assignment system.
"""

import sys
import os
import subprocess
import json
import time

# Add the parent directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

BASE_URL = "http://localhost:8000"

def run_curl(method, endpoint, data=None, headers=None):
    """Run a curl command and return the response"""
    cmd = ["curl", "-s", "-X", method, f"{BASE_URL}{endpoint}"]
    
    if headers:
        for key, value in headers.items():
            cmd.extend(["-H", f"{key}: {value}"])
    
    if data:
        cmd.extend(["-H", "Content-Type: application/json"])
        cmd.extend(["-d", json.dumps(data)])
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            try:
                return json.loads(result.stdout)
            except json.JSONDecodeError:
                return {"raw_response": result.stdout}
        else:
            return {"error": result.stderr, "stdout": result.stdout}
    except subprocess.TimeoutExpired:
        return {"error": "Request timed out"}
    except Exception as e:
        return {"error": str(e)}

def test_workflow():
    """Test the complete admin/agent workflow"""
    print("🚀 Testing Complete Admin/Agent Workflow")
    print("=" * 50)
    
    # Step 1: Customer starts a conversation
    print("\n📝 Step 1: Customer starts a conversation")
    conversation_data = {
        "user_id": "customer-xyz-789"
    }
    
    response = run_curl("POST", "/api/conversations/", conversation_data)
    if "error" in response:
        print(f"❌ Failed to create conversation: {response['error']}")
        return False
    
    conversation_id = response.get("id")
    print(f"✅ Conversation created with ID: {conversation_id}")
    print(f"   Status: {response.get('status')}")
    print(f"   Assigned Agent: {response.get('assigned_agent_id')}")
    
    # Step 2: Admin lists all conversations
    print("\n📋 Step 2: Admin lists all conversations")
    headers = {"X-User-Role": "admin"}
    response = run_curl("GET", "/api/conversations/", headers=headers)
    
    if "error" in response:
        print(f"❌ Failed to list conversations: {response['error']}")
        return False
    
    print(f"✅ Found {len(response)} conversations")
    for conv in response:
        print(f"   - ID: {conv['id']}, User: {conv['user_id']}, Status: {conv['status']}")
    
    # Step 3: Admin lists available agents
    print("\n👥 Step 3: Admin lists available agents")
    response = run_curl("GET", "/api/users/agents", headers=headers)
    
    if "error" in response:
        print(f"❌ Failed to list agents: {response['error']}")
        return False
    
    print(f"✅ Found {len(response)} agents")
    agent_id = None
    for agent in response:
        print(f"   - ID: {agent['id']}, Name: {agent['full_name']}, Email: {agent['email']}")
        if agent_id is None:
            agent_id = agent['id']
    
    if not agent_id:
        print("❌ No agents found")
        return False
    
    # Step 4: Admin assigns conversation to agent
    print(f"\n🔗 Step 4: Admin assigns conversation {conversation_id} to agent {agent_id}")
    response = run_curl("POST", f"/api/conversations/{conversation_id}/assign/{agent_id}", headers=headers)
    
    if "error" in response:
        print(f"❌ Failed to assign agent: {response['error']}")
        return False
    
    print(f"✅ Agent assigned successfully")
    print(f"   Status: {response.get('status')}")
    print(f"   Assigned Agent ID: {response.get('assigned_agent_id')}")
    
    # Step 5: Verify the assignment
    print(f"\n🔍 Step 5: Verify the assignment")
    response = run_curl("GET", f"/api/conversations/{conversation_id}")
    
    if "error" in response:
        print(f"❌ Failed to get conversation: {response['error']}")
        return False
    
    print(f"✅ Assignment verified")
    print(f"   Status: {response.get('status')}")
    print(f"   Assigned Agent ID: {response.get('assigned_agent_id')}")
    
    # Step 6: Agent views their conversations
    print(f"\n👤 Step 6: Agent views their conversations")
    agent_headers = {"X-User-Role": "agent", "X-User-Id": str(agent_id)}
    response = run_curl("GET", f"/api/conversations/agent/{agent_id}", headers=agent_headers)
    
    if "error" in response:
        print(f"❌ Failed to get agent conversations: {response['error']}")
        return False
    
    print(f"✅ Agent has {len(response)} assigned conversations")
    for conv in response:
        print(f"   - ID: {conv['id']}, User: {conv['user_id']}, Status: {conv['status']}")
    
    # Step 7: Admin views unassigned conversations
    print(f"\n📊 Step 7: Admin views unassigned conversations")
    response = run_curl("GET", "/api/conversations/unassigned", headers=headers)
    
    if "error" in response:
        print(f"❌ Failed to get unassigned conversations: {response['error']}")
        return False
    
    print(f"✅ Found {len(response)} unassigned conversations")
    
    # Step 8: Test permissions - Agent tries to access admin endpoint
    print(f"\n🔒 Step 8: Test permissions - Agent tries to access admin endpoint")
    response = run_curl("GET", "/api/users/", headers=agent_headers)
    
    if "error" in response or response.get("detail") == "Only admins can view all users":
        print("✅ Permission check passed - Agent correctly denied access")
    else:
        print("❌ Permission check failed - Agent should not have access")
        return False
    
    # Step 9: Test API info endpoints
    print(f"\n📖 Step 9: Test API info endpoints")
    response = run_curl("GET", "/")
    
    if "error" in response:
        print(f"❌ Failed to get API info: {response['error']}")
        return False
    
    print("✅ API info endpoint working")
    print(f"   App: {response.get('message')}")
    print(f"   Version: {response.get('version')}")
    
    return True

def test_user_creation():
    """Test user creation"""
    print("\n👤 Testing User Creation")
    print("-" * 30)
    
    headers = {"X-User-Role": "admin"}
    user_data = {
        "email": "<EMAIL>",
        "password": "agent2password",
        "full_name": "Agent Two",
        "role": "agent"
    }
    
    response = run_curl("POST", "/api/users/", user_data, headers)
    
    if "error" in response:
        print(f"❌ Failed to create user: {response['error']}")
        return False
    
    print(f"✅ User created successfully")
    print(f"   ID: {response.get('id')}")
    print(f"   Name: {response.get('full_name')}")
    print(f"   Email: {response.get('email')}")
    print(f"   Role: {response.get('role')}")
    
    return True

def main():
    """Run all tests"""
    print("🎯 Yupcha Customer Bot AI - Complete Workflow Test")
    print("=" * 60)
    
    # Test user creation
    if not test_user_creation():
        print("\n❌ User creation tests failed!")
        return
    
    # Test main workflow
    if not test_workflow():
        print("\n❌ Workflow tests failed!")
        return
    
    print("\n" + "=" * 60)
    print("🎉 ALL TESTS PASSED!")
    print("\n✅ Features Implemented:")
    print("   🔐 User Roles (Admin/Agent)")
    print("   📝 Conversation Assignment")
    print("   🛡️  Permission System")
    print("   📊 Admin Dashboard Endpoints")
    print("   👤 Agent Conversation Management")
    print("   🔗 Scalar API Documentation")
    print("\n🚀 Ready for Production Use!")
    print("\n📚 API Documentation:")
    print("   📖 Swagger UI: http://localhost:8000/docs")
    print("   📘 ReDoc: http://localhost:8000/redoc")
    print("   ⚡ Scalar: http://localhost:8000/scalar")

if __name__ == "__main__":
    main()
