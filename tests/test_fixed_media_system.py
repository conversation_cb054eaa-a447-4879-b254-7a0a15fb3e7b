#!/usr/bin/env python3
"""
Test script for the fixed media system with proper file handling
and message deletion functionality.
"""

import asyncio
import aiohttp
import json
import io
from PIL import Image
import os

BASE_URL = "http://localhost:8000"

async def create_test_image():
    """Create a simple test image in memory"""
    # Create a 100x100 red image
    img = Image.new('RGB', (100, 100), color='red')
    img_bytes = io.BytesIO()
    img.save(img_bytes, format='PNG')
    img_bytes.seek(0)
    return img_bytes

async def login_admin(session):
    """Login as admin and get session cookie"""
    login_data = {
        'username': '<EMAIL>',
        'password': 'adminpassword'
    }
    
    async with session.post(f"{BASE_URL}/api/auth/login", data=login_data) as response:
        if response.status == 200:
            print("✅ Admin login successful")
            return True
        else:
            print(f"❌ Admin login failed: {response.status}")
            return False

async def test_file_upload(session):
    """Test the fixed file upload functionality"""
    print("\n🔧 Testing Fixed File Upload System")
    
    # Create test image
    test_image = await create_test_image()
    
    # Prepare form data
    data = aiohttp.FormData()
    data.add_field('file', test_image, filename='test_image.png', content_type='image/png')
    
    async with session.post(f"{BASE_URL}/api/media/upload", data=data) as response:
        if response.status == 200:
            result = await response.json()
            print(f"✅ File upload successful!")
            print(f"   Asset ID: {result['asset_id']}")
            print(f"   File Type: {result['file_type']}")
            print(f"   File Size: {result['file_size']} bytes")
            print(f"   S3 URL: {result['s3_url']}")
            return result['asset_id']
        else:
            error_text = await response.text()
            print(f"❌ File upload failed: {response.status}")
            print(f"   Error: {error_text}")
            return None

async def test_asset_management(session, asset_id):
    """Test asset management endpoints"""
    print("\n📁 Testing Asset Management")
    
    # List assets
    async with session.get(f"{BASE_URL}/api/media/assets") as response:
        if response.status == 200:
            assets = await response.json()
            print(f"✅ Found {len(assets)} assets in system")
        else:
            print(f"❌ Failed to list assets: {response.status}")
    
    # Get specific asset
    if asset_id:
        async with session.get(f"{BASE_URL}/api/media/assets/{asset_id}") as response:
            if response.status == 200:
                asset = await response.json()
                print(f"✅ Asset details retrieved:")
                print(f"   Original filename: {asset['original_filename']}")
                print(f"   File type: {asset['file_type']}")
                print(f"   Dimensions: {asset.get('width', 'N/A')}x{asset.get('height', 'N/A')}")
            else:
                print(f"❌ Failed to get asset details: {response.status}")

async def test_message_with_deletion(session):
    """Test message creation with deletion field"""
    print("\n💬 Testing Message System with Deletion Field")
    
    # Create a customer first
    customer_data = {
        "customer_id": "test-deletion-customer",
        "name": "Test Deletion Customer",
        "email": "<EMAIL>"
    }
    
    async with session.post(f"{BASE_URL}/api/customers/", json=customer_data) as response:
        if response.status == 200:
            customer = await response.json()
            customer_id = customer['id']
            print(f"✅ Customer created: ID {customer_id}")
        else:
            print(f"❌ Failed to create customer: {response.status}")
            return
    
    # Create conversation
    conv_data = {
        "customer_id": customer_id,
        "organization_id": 1
    }
    
    async with session.post(f"{BASE_URL}/api/conversations/", json=conv_data) as response:
        if response.status == 200:
            conversation = await response.json()
            conv_id = conversation['id']
            print(f"✅ Conversation created: ID {conv_id}")
        else:
            print(f"❌ Failed to create conversation: {response.status}")
            return
    
    # Test message creation with deletion field
    message_data = {
        "conversation_id": conv_id,
        "content": "Test message with deletion field",
        "sender": "customer",
        "message_type": "text",
        "deleted": False,  # Test the new deletion field
        "customer_id": customer_id
    }
    
    async with session.post(f"{BASE_URL}/api/conversations/{conv_id}/messages", json=message_data) as response:
        if response.status == 200:
            message = await response.json()
            print(f"✅ Message created with deletion field:")
            print(f"   Message ID: {message['id']}")
            print(f"   Content: {message['content']}")
            print(f"   Deleted: {message['deleted']}")
            return message['id']
        else:
            error_text = await response.text()
            print(f"❌ Failed to create message: {response.status}")
            print(f"   Error: {error_text}")
            return None

async def test_media_message(session, asset_id):
    """Test creating a media message with asset"""
    if not asset_id:
        print("\n⚠️  Skipping media message test (no asset available)")
        return
    
    print("\n🖼️  Testing Media Message Creation")
    
    # Create customer and conversation for media test
    customer_data = {
        "customer_id": "media-test-customer",
        "name": "Media Test Customer", 
        "email": "<EMAIL>"
    }
    
    async with session.post(f"{BASE_URL}/api/customers/", json=customer_data) as response:
        if response.status == 200:
            customer = await response.json()
            customer_id = customer['id']
        else:
            print(f"❌ Failed to create media customer: {response.status}")
            return
    
    conv_data = {
        "customer_id": customer_id,
        "organization_id": 1
    }
    
    async with session.post(f"{BASE_URL}/api/conversations/", json=conv_data) as response:
        if response.status == 200:
            conversation = await response.json()
            conv_id = conversation['id']
        else:
            print(f"❌ Failed to create media conversation: {response.status}")
            return
    
    # Create media message
    media_message_data = {
        "conversation_id": conv_id,
        "content": "Check out this image!",
        "sender": "customer",
        "message_type": "image",
        "asset_id": asset_id,
        "deleted": False,
        "customer_id": customer_id
    }
    
    async with session.post(f"{BASE_URL}/api/conversations/{conv_id}/messages", json=media_message_data) as response:
        if response.status == 200:
            message = await response.json()
            print(f"✅ Media message created successfully:")
            print(f"   Message ID: {message['id']}")
            print(f"   Message Type: {message['message_type']}")
            print(f"   Asset ID: {message['asset_id']}")
            print(f"   Content: {message['content']}")
            print(f"   Deleted: {message['deleted']}")
        else:
            error_text = await response.text()
            print(f"❌ Failed to create media message: {response.status}")
            print(f"   Error: {error_text}")

async def main():
    """Main test function"""
    print("🚀 Testing Fixed Media System with Message Deletion")
    print("=" * 60)
    
    async with aiohttp.ClientSession() as session:
        # Login first
        if not await login_admin(session):
            return
        
        # Test file upload (fixed version)
        asset_id = await test_file_upload(session)
        
        # Test asset management
        await test_asset_management(session, asset_id)
        
        # Test message with deletion field
        await test_message_with_deletion(session)
        
        # Test media message
        await test_media_message(session, asset_id)
    
    print("\n" + "=" * 60)
    print("🎉 All tests completed!")
    print("\n✅ Fixed Issues:")
    print("   • File upload now reads content only once")
    print("   • S3Manager constants moved to class")
    print("   • Message table has 'deleted' boolean field")
    print("   • Media messages work with asset relationships")
    print("   • Proper error handling and validation")

if __name__ == "__main__":
    asyncio.run(main())
