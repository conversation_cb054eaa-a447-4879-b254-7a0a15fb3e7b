<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Yupcha Media Upload Demo</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .upload-section {
            border: 2px dashed #ddd;
            padding: 30px;
            text-align: center;
            margin-bottom: 30px;
            border-radius: 8px;
        }
        .upload-section.dragover {
            border-color: #007bff;
            background-color: #f8f9fa;
        }
        .file-input {
            margin: 20px 0;
        }
        .file-input input[type="file"] {
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            width: 100%;
            max-width: 400px;
        }
        .upload-btn {
            background-color: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
        }
        .upload-btn:hover {
            background-color: #0056b3;
        }
        .upload-btn:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .status {
            margin: 20px 0;
            padding: 10px;
            border-radius: 4px;
            text-align: center;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .assets-list {
            margin-top: 30px;
        }
        .asset-item {
            display: flex;
            align-items: center;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-bottom: 10px;
            background-color: #f8f9fa;
        }
        .asset-info {
            flex: 1;
            margin-left: 15px;
        }
        .asset-preview {
            width: 60px;
            height: 60px;
            border-radius: 4px;
            object-fit: cover;
            background-color: #e9ecef;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
        }
        .asset-url {
            font-family: monospace;
            font-size: 12px;
            color: #6c757d;
            word-break: break-all;
        }
        .login-section {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Yupcha Media Upload Demo</h1>
        
        <div class="login-section" id="loginSection">
            <h3>🔐 Login Required</h3>
            <p>You need to login first to upload media files.</p>
            <button class="upload-btn" onclick="loginAsAdmin()">Login as Admin</button>
            <div id="loginStatus"></div>
        </div>

        <div id="uploadSection" class="hidden">
            <div class="upload-section" id="dropZone">
                <h3>📤 Upload Media Files</h3>
                <p>Drag and drop files here or click to select</p>
                <div class="file-input">
                    <input type="file" id="fileInput" multiple accept="image/*,video/*,audio/*,.pdf,.txt,.doc,.docx">
                </div>
                <button class="upload-btn" onclick="uploadFiles()" id="uploadBtn">Upload Files</button>
            </div>

            <div id="uploadStatus"></div>

            <div class="assets-list">
                <h3>📁 Uploaded Assets</h3>
                <button class="upload-btn" onclick="loadAssets()">Refresh Assets</button>
                <div id="assetsList"></div>
            </div>
        </div>
    </div>

    <script>
        let isLoggedIn = false;

        function showStatus(message, type = 'info') {
            const statusDiv = document.getElementById('uploadStatus');
            statusDiv.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        function showLoginStatus(message, type = 'info') {
            const statusDiv = document.getElementById('loginStatus');
            statusDiv.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        async function loginAsAdmin() {
            try {
                const response = await fetch('http://localhost:8000/api/auth/login', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                    body: 'username=<EMAIL>&password=adminpassword',
                });

                if (response.ok) {
                    isLoggedIn = true;
                    showLoginStatus('✅ Login successful!', 'success');
                    document.getElementById('loginSection').classList.add('hidden');
                    document.getElementById('uploadSection').classList.remove('hidden');
                    loadAssets();
                } else {
                    const error = await response.text();
                    showLoginStatus(`❌ Login failed: ${error}`, 'error');
                }
            } catch (error) {
                showLoginStatus(`❌ Login error: ${error.message}`, 'error');
            }
        }

        async function uploadFiles() {
            const fileInput = document.getElementById('fileInput');
            if (fileInput.files.length === 0) return;

            const uploadBtn = document.getElementById('uploadBtn');
            uploadBtn.disabled = true;
            uploadBtn.textContent = 'Uploading...';

            try {
                for (const file of fileInput.files) {
                    showStatus(`Uploading ${file.name}...`, 'info');
                    const formData = new FormData();
                    formData.append('file', file);

                    // *** THE FIX IS HERE ***
                    const response = await fetch('http://localhost:8000/api/media/upload', {
                        method: 'POST',
                        body: formData,
                    });

                    if (!response.ok) {
                        const errorText = await response.text();
                        throw new Error(`Upload failed (${response.status}): ${errorText}`);
                    }
                    
                    const result = await response.json();
                    showStatus(`✅ ${file.name} uploaded! Asset ID: ${result.asset_id}`, 'success');
                }
                loadAssets();
            } catch (error) {
                showStatus(error.message, 'error');
            } finally {
                uploadBtn.disabled = false;
                uploadBtn.textContent = 'Upload Files';
                fileInput.value = '';
            }
        }

        async function loadAssets() {
            try {
                // *** THE FIX IS HERE ***
                const response = await fetch('http://localhost:8000/api/media/assets', {
                    method: 'GET',
                });

                if (!response.ok) {
                    const errorText = await response.text();
                    throw new Error(`Failed to load assets (${response.status}): ${errorText}`);
                }
                const assets = await response.json();
                displayAssets(assets);
            } catch (error) {
                showStatus(error.message, 'error');
            }
        }

        function displayAssets(assets) {
            const assetsList = document.getElementById('assetsList');
            if (assets.length === 0) {
                assetsList.innerHTML = '<p>No assets uploaded yet.</p>';
                return;
            }
            assetsList.innerHTML = assets.map(asset => {
                const icon = getFileIcon(asset.file_type);
                const sizeKB = Math.round(asset.file_size / 1024);
                return `
                    <div class="asset-item">
                        <div class="asset-preview">${icon}</div>
                        <div class="asset-info">
                            <strong>${asset.original_filename}</strong><br>
                            <small>Type: ${asset.file_type} | Size: ${sizeKB} KB | ID: ${asset.id}</small><br>
                            <div class="asset-url">${asset.s3_url}</div>
                        </div>
                    </div>`;
            }).join('');
        }

        function getFileIcon(fileType) {
            const icons = {
                'image': '🖼️', 'video': '🎥', 'audio': '🎵', 'gif': '🎬', 'document': '📄'
            };
            return icons[fileType] || '📎';
        }

        // Drag and drop functionality
        const dropZone = document.getElementById('dropZone');
        const fileInput = document.getElementById('fileInput');

        dropZone.addEventListener('click', () => {
            if (isLoggedIn) {
                fileInput.click();
            }
        });

        dropZone.addEventListener('dragover', (e) => {
            e.preventDefault();
            dropZone.classList.add('dragover');
        });

        dropZone.addEventListener('dragleave', () => {
            dropZone.classList.remove('dragover');
        });

        dropZone.addEventListener('drop', (e) => {
            e.preventDefault();
            dropZone.classList.remove('dragover');
            
            if (isLoggedIn) {
                fileInput.files = e.dataTransfer.files;
            }
        });

        // Check if already logged in on page load
        window.onload = async function() {
            try {
                const response = await fetch('http://localhost:8000/api/auth/me', { method: 'GET' });
                if (response.ok) {
                    isLoggedIn = true;
                    document.getElementById('loginSection').classList.add('hidden');
                    document.getElementById('uploadSection').classList.remove('hidden');
                    loadAssets();
                }
            } catch (error) {
                console.log("Not logged in. Showing login form.");
            }
        };
    </script>
</body>
</html>
