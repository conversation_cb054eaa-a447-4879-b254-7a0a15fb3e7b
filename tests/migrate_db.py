#!/usr/bin/env python3
"""
Database Migration Script
Run this to ensure all tables are created and up to date.
"""

import asyncio
from sqlalchemy.ext.asyncio import create_async_engine
from app.db.base import Base
from app.core.config import settings

# Import all models to ensure they're registered
from app.models.user import User
from app.models.organization import Organization
from app.models.team import Team
from app.models.customer import Customer
from app.models.conversation import Conversation
from app.models.message import Message

async def create_tables():
    """Create all database tables"""
    engine = create_async_engine(settings.DATABASE_URL, echo=True)
    
    async with engine.begin() as conn:
        # Drop all tables (be careful in production!)
        print("Dropping all tables...")
        await conn.run_sync(Base.metadata.drop_all)
        
        # Create all tables
        print("Creating all tables...")
        await conn.run_sync(Base.metadata.create_all)
    
    await engine.dispose()
    print("Database migration completed successfully!")

if __name__ == "__main__":
    asyncio.run(create_tables())
