import asyncio
import websockets
import json
import uuid
import aioconsole  
# --- Configuration ---
# IMPORTANT: Change these values to match a real conversation in your database.
WS_BASE_URL = "ws://localhost:8000/api/ws"
CONVERSATION_ID = 2
ORGANIZATION_ID = 6

# Generate a unique customer ID for each run to simulate a new user,
# or use a static one to simulate a returning user.
CUSTOMER_ID = f"interactive-customer-{uuid.uuid4().hex[:6]}"

async def interactive_customer_chat():
    """
    Simulates a customer connecting to a conversation and allows you to
    send messages interactively from the command line.
    """
    uri = f"{WS_BASE_URL}/chat/{CONVERSATION_ID}?customer_id={CUSTOMER_ID}"
    
    print("==================================================")
    print("🚀 Yupcha Interactive Customer Chat Client")
    print("==================================================")
    print(f"👤 Acting as Customer ID: {CUSTOMER_ID}")
    print(f"💬 Connecting to Conversation ID: {CONVERSATION_ID}")
    print(f"🔗 WebSocket URL: {uri}")
    print("--------------------------------------------------")
    print("Type a message and press Enter to send.")
    print("Type 'quit', 'exit', or 'q' to disconnect.")
    print("--------------------------------------------------")

    try:
        async with websockets.connect(uri) as websocket:
            print("✅ Connection successful! You can start chatting.")
            
            # Task 1: Listen for incoming messages from the server (bot/agent)
            async def receive_messages():
                try:
                    async for message in websocket:
                        data = json.loads(message)
                        sender = data.get('sender', 'system')
                        content = data.get('content', data.get('detail', 'No content'))
                        
                        # Use ANSI escape codes for color
                        # Green for received messages
                        print(f"\n\033[92m Gelen  [{sender.upper()}]: {content}\033[0m")
                        # Reprint the input prompt
                        print(" Gönderilen : ", end="", flush=True)
                except websockets.ConnectionClosed:
                    print("\n👂 Listener: Connection closed by server.")

            # Task 2: Listen for user input from the terminal and send it
            async def send_messages():
                while True:
                    # Use aioconsole for non-blocking input
                    message_to_send = await aioconsole.ainput(" Gönderilen : ")
                    
                    if message_to_send.lower() in ['quit', 'exit', 'q']:
                        print("👋 Disconnecting...")
                        break

                    if not message_to_send:
                        continue

                    payload = {
                        "content": message_to_send,
                        "sender": "customer",
                        "message_type": "text"
                    }
                    
                    await websocket.send(json.dumps(payload))

            # Run both tasks concurrently
            # The sender task will complete when the user types 'quit'
            # which will then cause the whole 'with' block to exit, closing the connection.
            receiver_task = asyncio.create_task(receive_messages())
            sender_task = asyncio.create_task(send_messages())
            
            await sender_task
            receiver_task.cancel() # Clean up the listener task

    except websockets.exceptions.ConnectionClosedError as e:
        print(f"\n❌ Connection closed unexpectedly: {e.code} {e.reason}")
    except Exception as e:
        print(f"\n❌ An unexpected error occurred: {e}")
    finally:
        print("--------------------------------------------------")
        print("⏹️ Script finished.")

if __name__ == "__main__":
    try:
        asyncio.run(interactive_customer_chat())
    except KeyboardInterrupt:
        print("\n👋 Script interrupted by user.")