#!/usr/bin/env python3
"""
API tests for Yupcha Customer Bot AI.
Test all API endpoints and functionality.
"""

import asyncio
import json
import sys
import os

# Add the parent directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from fastapi.testclient import TestClient
from sqlalchemy.ext.asyncio import AsyncSession

from app.main import app
from app.db.session import AsyncSessionLocal
from app.crud import crud_chat
from app.schemas.chat import ConversationCreate, MessageCreate

# Create test client
client = TestClient(app)

def test_root_endpoint():
    """Test the root endpoint"""
    response = client.get("/")
    assert response.status_code == 200
    data = response.json()
    assert "message" in data
    assert "version" in data
    assert "endpoints" in data
    print("✓ Root endpoint test passed")

def test_health_endpoint():
    """Test the health check endpoint"""
    response = client.get("/health")
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "healthy"
    assert "app_name" in data
    assert "version" in data
    print("✓ Health endpoint test passed")

def test_api_info_endpoint():
    """Test the API info endpoint"""
    response = client.get("/api")
    assert response.status_code == 200
    data = response.json()
    assert "api_version" in data
    assert "endpoints" in data
    assert "documentation" in data
    print("✓ API info endpoint test passed")

def test_openapi_docs():
    """Test that OpenAPI documentation is accessible"""
    response = client.get("/openapi.json")
    assert response.status_code == 200
    data = response.json()
    assert "openapi" in data
    assert "info" in data
    assert "paths" in data
    print("✓ OpenAPI documentation test passed")

def test_create_conversation():
    """Test creating a conversation"""
    conversation_data = {
        "user_id": "test_user_api",
        "title": "API Test Conversation"
    }
    
    response = client.post("/api/conversations/", json=conversation_data)
    assert response.status_code == 200
    data = response.json()
    assert "id" in data
    assert data["user_id"] == "test_user_api"
    assert data["title"] == "API Test Conversation"
    print(f"✓ Create conversation test passed - ID: {data['id']}")
    return data["id"]

def test_get_conversation():
    """Test getting a conversation"""
    # First create a conversation
    conversation_id = test_create_conversation()
    
    # Then get it
    response = client.get(f"/api/conversations/{conversation_id}")
    assert response.status_code == 200
    data = response.json()
    assert data["id"] == conversation_id
    assert data["user_id"] == "test_user_api"
    print("✓ Get conversation test passed")

def test_list_conversations():
    """Test listing conversations"""
    response = client.get("/api/conversations/")
    assert response.status_code == 200
    data = response.json()
    assert isinstance(data, list)
    print(f"✓ List conversations test passed - Found {len(data)} conversations")

def test_get_conversation_messages():
    """Test getting conversation messages"""
    # Create a conversation first
    conversation_id = test_create_conversation()
    
    # Get messages (should be empty initially)
    response = client.get(f"/api/conversations/{conversation_id}/messages")
    assert response.status_code == 200
    data = response.json()
    assert isinstance(data, list)
    print(f"✓ Get conversation messages test passed - Found {len(data)} messages")

def test_nonexistent_conversation():
    """Test getting a non-existent conversation"""
    response = client.get("/api/conversations/99999")
    assert response.status_code == 404
    print("✓ Non-existent conversation test passed")

async def test_async_crud_operations():
    """Test async CRUD operations directly"""
    async with AsyncSessionLocal() as db:
        # Test creating a conversation
        conversation_data = ConversationCreate(
            user_id="test_async_user",
            title="Async Test Conversation"
        )
        
        conversation = await crud_chat.create_conversation(db=db, conversation=conversation_data)
        assert conversation.id is not None
        assert conversation.user_id == "test_async_user"
        print(f"✓ Async create conversation test passed - ID: {conversation.id}")
        
        # Test retrieving the conversation
        retrieved = await crud_chat.get_conversation(db=db, conversation_id=conversation.id)
        assert retrieved is not None
        assert retrieved.user_id == "test_async_user"
        print("✓ Async get conversation test passed")
        
        # Test creating a message
        message_data = MessageCreate(
            conversation_id=conversation.id,
            content="Test message content",
            sender="user",
            message_type="text"
        )
        
        message = await crud_chat.create_message(db=db, message=message_data)
        assert message.id is not None
        assert message.content == "Test message content"
        print(f"✓ Async create message test passed - ID: {message.id}")
        
        # Test getting messages
        messages = await crud_chat.get_messages_by_conversation(db=db, conversation_id=conversation.id)
        assert len(messages) >= 1
        assert messages[0].content == "Test message content"
        print("✓ Async get messages test passed")

def run_all_tests():
    """Run all tests"""
    print("Yupcha Customer Bot AI - API Tests")
    print("=" * 40)
    
    try:
        # Test API endpoints
        test_root_endpoint()
        test_health_endpoint()
        test_api_info_endpoint()
        test_openapi_docs()
        
        # Test conversation endpoints
        test_create_conversation()
        test_get_conversation()
        test_list_conversations()
        test_get_conversation_messages()
        test_nonexistent_conversation()
        
        # Test async operations
        asyncio.run(test_async_crud_operations())
        
        print("\n✅ All API tests passed!")
        print("\n🎯 API Endpoints Available:")
        print("📋 GET  /              - Root endpoint with API info")
        print("💚 GET  /health        - Health check")
        print("📖 GET  /api           - API documentation")
        print("📚 GET  /docs          - Swagger UI")
        print("📘 GET  /redoc         - ReDoc UI")
        print("🔧 GET  /openapi.json  - OpenAPI schema")
        print("\n💬 Conversation Endpoints:")
        print("📝 POST /api/conversations/           - Create conversation")
        print("📋 GET  /api/conversations/           - List conversations")
        print("👁️  GET  /api/conversations/{id}      - Get conversation")
        print("💬 GET  /api/conversations/{id}/messages - Get messages")
        print("\n🔌 WebSocket Endpoint:")
        print("⚡ WS   /api/ws/chat/{id}?user_type=user&user_id=123")
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    run_all_tests()
