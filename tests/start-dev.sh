#!/bin/bash

# Yupcha Customer Bot AI - Development Startup Script
# This script starts both the backend and frontend development servers

echo "🚀 Starting Yupcha Customer Bot AI Development Environment"
echo "============================================================"

# Function to cleanup background processes on exit
cleanup() {
    echo ""
    echo "🛑 Shutting down development servers..."
    kill $BACKEND_PID 2>/dev/null
    kill $FRONTEND_PID 2>/dev/null
    echo "✅ Development servers stopped"
    exit 0
}

# Set up trap to cleanup on script exit
trap cleanup SIGINT SIGTERM EXIT

# Check if uv is available
if ! command -v uv &> /dev/null; then
    echo "❌ Error: 'uv' command not found. Please install uv first."
    echo "   Visit: https://docs.astral.sh/uv/getting-started/installation/"
    exit 1
fi

# Check if npm is available
if ! command -v npm &> /dev/null; then
    echo "❌ Error: 'npm' command not found. Please install Node.js and npm first."
    echo "   Visit: https://nodejs.org/"
    exit 1
fi

# Start backend server
echo "🔧 Starting backend server..."
echo "   Command: uv run uvicorn app.main:app --reload --host 0.0.0.0 --port 8000"
uv run uvicorn app.main:app --reload --host 0.0.0.0 --port 8000 &
BACKEND_PID=$!

# Wait a moment for backend to start
sleep 3

# Check if backend started successfully
if ! kill -0 $BACKEND_PID 2>/dev/null; then
    echo "❌ Failed to start backend server"
    exit 1
fi

echo "✅ Backend server started (PID: $BACKEND_PID)"
echo "   URL: http://0.0.0.0:8000"
echo "   API Docs: http://0.0.0.0:8000/docs"

# Start frontend development server
echo ""
echo "🎨 Starting frontend development server..."
echo "   Command: cd frontend-vue && npm run dev"

cd frontend-vue

# Check if node_modules exists, if not run npm install
if [ ! -d "node_modules" ]; then
    echo "📦 Installing frontend dependencies..."
    npm install
fi

npm run dev &
FRONTEND_PID=$!

# Wait a moment for frontend to start
sleep 3

# Check if frontend started successfully
if ! kill -0 $FRONTEND_PID 2>/dev/null; then
    echo "❌ Failed to start frontend server"
    kill $BACKEND_PID 2>/dev/null
    exit 1
fi

echo "✅ Frontend server started (PID: $FRONTEND_PID)"
echo "   URL: http://localhost:5173"
echo "   Vue DevTools: http://localhost:5173/__devtools__/"

echo ""
echo "🎉 Development environment is ready!"
echo "============================================================"
echo "📱 Frontend:  http://localhost:5173"
echo "🔧 Backend:   http://0.0.0.0:8000"
echo "📚 API Docs:  http://0.0.0.0:8000/docs"
echo "🛠️  DevTools:  http://localhost:5173/__devtools__/"
echo ""
echo "Demo Credentials:"
echo "  Admin: <EMAIL> / adminpassword"
echo "  Agent: <EMAIL> / agentpassword"
echo ""
echo "Press Ctrl+C to stop both servers"
echo "============================================================"

# Wait for both processes
wait $BACKEND_PID $FRONTEND_PID
