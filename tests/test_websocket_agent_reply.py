#!/usr/bin/env python3
"""
Test script to demonstrate agent-customer WebSocket communication
"""

import asyncio
import websockets
import json
import requests
import sys

BASE_URL = "http://localhost:8000"
WS_URL = "ws://localhost:8000"

async def test_agent_customer_communication():
    """Test that agents can reply to customers via WebSocket"""
    
    print("🚀 Testing Agent-Customer WebSocket Communication")
    print("=" * 50)
    
    # Step 1: Login as admin to create test data
    print("📝 Step 1: Setting up test data...")
    
    login_response = requests.post(
        f"{BASE_URL}/api/auth/login",
        data={"username": "<EMAIL>", "password": "adminpassword"}
    )
    
    if login_response.status_code != 200:
        print("❌ Admin login failed")
        return
    
    cookies = login_response.cookies
    
    # Create a customer
    customer_data = {
        "customer_id": "ws-test-customer",
        "name": "WebSocket Test Customer",
        "email": "<EMAIL>"
    }
    
    customer_response = requests.post(
        f"{BASE_URL}/api/customers/",
        json=customer_data
    )
    
    if customer_response.status_code == 200:
        customer = customer_response.json()
        customer_db_id = customer["id"]
        print(f"✅ Customer created: {customer['name']} (ID: {customer_db_id})")
    else:
        print("❌ Failed to create customer")
        return
    
    # Create a conversation
    conv_data = {
        "customer_id": customer_db_id,
        "organization_id": 1  # Default organization
    }
    
    conv_response = requests.post(
        f"{BASE_URL}/api/conversations/",
        json=conv_data
    )
    
    if conv_response.status_code == 200:
        conversation = conv_response.json()
        conversation_id = conversation["id"]
        print(f"✅ Conversation created: ID {conversation_id}")
    else:
        print("❌ Failed to create conversation")
        return
    
    # Step 2: Test WebSocket communication
    print(f"\n💬 Step 2: Testing WebSocket communication for conversation {conversation_id}")
    
    # Customer WebSocket URL
    customer_ws_url = f"{WS_URL}/api/ws/chat/{conversation_id}?customer_id=ws-test-customer"
    
    # Agent WebSocket URL (we'll need to get session cookie)
    agent_login = requests.post(
        f"{BASE_URL}/api/auth/login",
        data={"username": "<EMAIL>", "password": "agentpassword"}
    )
    
    if agent_login.status_code != 200:
        print("❌ Agent login failed")
        return
    
    agent_cookies = agent_login.cookies
    agent_session_cookie = agent_cookies.get('yupcha_session')
    
    if not agent_session_cookie:
        print("❌ No session cookie found for agent")
        return
    
    agent_ws_url = f"{WS_URL}/api/ws/chat/{conversation_id}"
    
    print(f"🔗 Customer WebSocket: {customer_ws_url}")
    print(f"🔗 Agent WebSocket: {agent_ws_url}")
    
    # Test the communication
    try:
        # Connect customer
        print("\n👤 Connecting customer...")
        async with websockets.connect(customer_ws_url) as customer_ws:
            print("✅ Customer connected")
            
            # Wait for connection confirmation
            customer_msg = await customer_ws.recv()
            print(f"📨 Customer received: {customer_msg}")
            
            # Connect agent with session cookie
            print("\n👨‍💼 Connecting agent...")
            agent_headers = {"Cookie": f"yupcha_session={agent_session_cookie}"}
            
            async with websockets.connect(agent_ws_url, extra_headers=agent_headers) as agent_ws:
                print("✅ Agent connected")
                
                # Wait for agent connection confirmation
                agent_msg = await agent_ws.recv()
                print(f"📨 Agent received: {agent_msg}")
                
                # Customer sends a message
                print("\n💬 Customer sends message...")
                customer_message = {
                    "content": "Hello, I need help with my order!",
                    "type": "message"
                }
                await customer_ws.send(json.dumps(customer_message))
                print("📤 Customer sent: 'Hello, I need help with my order!'")
                
                # Both should receive the message
                print("\n📨 Waiting for message broadcasts...")
                
                # Customer receives their own message
                customer_echo = await customer_ws.recv()
                print(f"👤 Customer received: {json.loads(customer_echo)['content']}")
                
                # Agent receives the customer message
                agent_received = await customer_ws.recv()
                print(f"👨‍💼 Agent received: {json.loads(agent_received)['content']}")
                
                # Agent replies
                print("\n💬 Agent sends reply...")
                agent_message = {
                    "content": "Hi! I'm here to help. Can you provide your order number?",
                    "type": "message"
                }
                await agent_ws.send(json.dumps(agent_message))
                print("📤 Agent sent: 'Hi! I'm here to help. Can you provide your order number?'")
                
                # Both should receive the agent's message
                print("\n📨 Waiting for agent message broadcasts...")
                
                # Agent receives their own message
                agent_echo = await agent_ws.recv()
                print(f"👨‍💼 Agent received: {json.loads(agent_echo)['content']}")
                
                # Customer receives the agent message
                customer_received = await customer_ws.recv()
                print(f"👤 Customer received: {json.loads(customer_received)['content']}")
                
                print("\n🎉 SUCCESS! Agent-Customer communication working!")
                print("✅ Customer can send messages to agent")
                print("✅ Agent can reply to customer")
                print("✅ Both receive real-time messages")
                
    except Exception as e:
        print(f"❌ WebSocket test failed: {e}")
        return
    
    print("\n🧹 Cleaning up test data...")
    # Clean up (optional)
    requests.delete(f"{BASE_URL}/api/conversations/{conversation_id}", cookies=cookies)
    requests.delete(f"{BASE_URL}/api/customers/{customer_db_id}", cookies=cookies)
    print("✅ Test data cleaned up")

if __name__ == "__main__":
    print("Make sure the server is running on http://localhost:8000")
    print("Starting WebSocket test in 3 seconds...")
    
    import time
    time.sleep(3)
    
    asyncio.run(test_agent_customer_communication())
