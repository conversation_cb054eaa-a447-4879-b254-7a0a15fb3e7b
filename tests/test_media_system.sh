#!/bin/bash

# Test Media System with S3/MinIO Integration
# Tests file upload, asset management, and media messages

BASE_URL="http://localhost:8000"

echo "🚀 Yupcha Media System Test"
echo "============================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

print_step() {
    echo -e "\n${BLUE}$1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${YELLOW}ℹ️  $1${NC}"
}

# Step 1: Check if <PERSON><PERSON> is running
print_step "🗄️  Step 1: Check MinIO/S3 connectivity"
print_info "Make sure Min<PERSON> is running on localhost:9000"
print_info "Default credentials: minioadmin / minioadmin"

# Step 2: Admin login
print_step "🔐 Step 2: Admin login"
LOGIN_RESPONSE=$(curl -s -X POST "$BASE_URL/api/auth/login" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "username=<EMAIL>&password=adminpassword" \
  -c cookies.txt)

if echo $LOGIN_RESPONSE | grep -q "Login successful"; then
    print_success "Admin login successful"
else
    print_error "Admin login failed"
    echo $LOGIN_RESPONSE
    exit 1
fi

# Step 3: Test API endpoints
print_step "📚 Step 3: Check media API endpoints"
API_INFO=$(curl -s -X GET "$BASE_URL/docs" | grep -o "media" | wc -l)
if [ "$API_INFO" -gt 0 ]; then
    print_success "Media API endpoints available"
else
    print_info "Media endpoints might not be loaded yet"
fi

# Step 4: Create test image file
print_step "🖼️  Step 4: Create test media files"

# Create a simple test image (1x1 pixel PNG)
echo -n "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAI9jU77yQAAAABJRU5ErkJggg==" | base64 -d > test_image.png
print_success "Test image created: test_image.png"

# Create a test text file
echo "This is a test document for media upload" > test_document.txt
print_success "Test document created: test_document.txt"

# Step 5: Test file upload
print_step "📤 Step 5: Test file upload"

# Upload image
IMAGE_UPLOAD=$(curl -s -X POST "$BASE_URL/api/media/upload" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@test_image.png" \
  -b cookies.txt)

if echo $IMAGE_UPLOAD | grep -q "asset_id"; then
    IMAGE_ASSET_ID=$(echo $IMAGE_UPLOAD | jq -r '.asset_id')
    IMAGE_S3_URL=$(echo $IMAGE_UPLOAD | jq -r '.s3_url')
    print_success "Image uploaded successfully! Asset ID: $IMAGE_ASSET_ID"
    print_info "S3 URL: $IMAGE_S3_URL"
else
    print_error "Image upload failed"
    echo $IMAGE_UPLOAD
fi

# Upload document
DOC_UPLOAD=$(curl -s -X POST "$BASE_URL/api/media/upload" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@test_document.txt" \
  -b cookies.txt)

if echo $DOC_UPLOAD | grep -q "asset_id"; then
    DOC_ASSET_ID=$(echo $DOC_UPLOAD | jq -r '.asset_id')
    print_success "Document uploaded successfully! Asset ID: $DOC_ASSET_ID"
else
    print_error "Document upload failed"
    echo $DOC_UPLOAD
fi

# Step 6: Test asset listing
print_step "📋 Step 6: Test asset management"

ASSETS_LIST=$(curl -s -X GET "$BASE_URL/api/media/assets" -b cookies.txt)
ASSET_COUNT=$(echo $ASSETS_LIST | jq '. | length')
print_success "Found $ASSET_COUNT assets in the system"

# Get specific asset
if [ ! -z "$IMAGE_ASSET_ID" ]; then
    ASSET_DETAILS=$(curl -s -X GET "$BASE_URL/api/media/assets/$IMAGE_ASSET_ID" -b cookies.txt)
    ASSET_TYPE=$(echo $ASSET_DETAILS | jq -r '.file_type')
    ASSET_SIZE=$(echo $ASSET_DETAILS | jq -r '.file_size')
    print_success "Asset details: Type=$ASSET_TYPE, Size=$ASSET_SIZE bytes"
fi

# Step 7: Create conversation for media messages
print_step "💬 Step 7: Create conversation for media messages"

# Create customer
CUSTOMER_DATA='{
  "customer_id": "media-test-customer",
  "name": "Media Test Customer",
  "email": "<EMAIL>"
}'

CUSTOMER_RESPONSE=$(curl -s -X POST "$BASE_URL/api/customers/" \
  -H "Content-Type: application/json" \
  -d "$CUSTOMER_DATA")

CUSTOMER_ID=$(echo $CUSTOMER_RESPONSE | jq -r '.id')
print_success "Customer created with ID: $CUSTOMER_ID"

# Create conversation
CONV_DATA="{
  \"customer_id\": $CUSTOMER_ID,
  \"organization_id\": 1
}"

CONV_RESPONSE=$(curl -s -X POST "$BASE_URL/api/conversations/" \
  -H "Content-Type: application/json" \
  -d "$CONV_DATA")

CONV_ID=$(echo $CONV_RESPONSE | jq -r '.id')
print_success "Conversation created with ID: $CONV_ID"

# Step 8: Test media message types
print_step "📱 Step 8: Test different message types"

print_info "WebSocket Media Message Examples:"
echo ""
echo "📝 Text Message:"
echo '{"content": "Hello!", "type": "message", "message_type": "text"}'
echo ""
echo "🖼️  Image Message:"
echo "{\"content\": \"Check out this image!\", \"type\": \"message\", \"message_type\": \"image\", \"asset_id\": $IMAGE_ASSET_ID}"
echo ""
echo "📄 Document Message:"
echo "{\"content\": \"Here's a document\", \"type\": \"message\", \"message_type\": \"document\", \"asset_id\": $DOC_ASSET_ID}"
echo ""
echo "🎥 Video Message:"
echo '{"content": "Video message", "type": "message", "message_type": "video", "asset_id": 123}'
echo ""
echo "🎵 Audio Message:"
echo '{"content": "Voice note", "type": "message", "message_type": "audio", "asset_id": 456}'
echo ""
echo "🎬 GIF Message:"
echo '{"content": "Funny GIF", "type": "message", "message_type": "gif", "asset_id": 789}'

# Step 9: Test S3 URL accessibility
print_step "🌐 Step 9: Test S3 URL accessibility"

if [ ! -z "$IMAGE_S3_URL" ]; then
    S3_TEST=$(curl -s -o /dev/null -w "%{http_code}" "$IMAGE_S3_URL")
    if [ "$S3_TEST" == "200" ]; then
        print_success "S3 URL is accessible: $IMAGE_S3_URL"
    else
        print_error "S3 URL not accessible (HTTP $S3_TEST): $IMAGE_S3_URL"
        print_info "Make sure MinIO is running and bucket is public"
    fi
fi

# Step 10: Environment configuration
print_step "⚙️  Step 10: Environment configuration"

print_info "S3/MinIO Configuration (set in .env file):"
echo "   S3_ENDPOINT_URL=http://localhost:9000"
echo "   S3_ACCESS_KEY=minioadmin"
echo "   S3_SECRET_KEY=minioadmin"
echo "   S3_BUCKET_NAME=yupcha-media"
echo "   S3_REGION=us-east-1"

# Cleanup
print_step "🧹 Cleanup"
rm -f cookies.txt test_image.png test_document.txt

echo ""
echo "=================================================="
print_success "🎉 MEDIA SYSTEM TEST COMPLETED!"
echo ""
echo -e "${GREEN}✅ Media Features Implemented:${NC}"
echo "   📤 File upload (images, videos, audio, documents, GIFs)"
echo "   🗄️  S3/MinIO integration for storage"
echo "   📊 Asset management with metadata"
echo "   💬 Media message types in WebSocket"
echo "   🔗 Asset-Message relationships"
echo "   📱 Support for all media types"
echo ""
echo -e "${BLUE}🚀 Supported File Types:${NC}"
echo "   🖼️  Images: JPEG, PNG, WebP"
echo "   🎬 GIFs: Animated GIFs"
echo "   🎥 Videos: MP4, WebM, MOV, AVI"
echo "   🎵 Audio: MP3, WAV, OGG, M4A"
echo "   📄 Documents: PDF, TXT, DOC"
echo ""
echo -e "${YELLOW}📚 API Endpoints:${NC}"
echo "   POST /api/media/upload - Upload files"
echo "   GET /api/media/assets - List assets"
echo "   GET /api/media/assets/{id} - Get asset details"
echo "   DELETE /api/media/assets/{id} - Delete asset"
echo ""
echo -e "${YELLOW}🔌 WebSocket Integration:${NC}"
echo "   Send media messages with asset_id"
echo "   Support for all message types"
echo "   Real-time media sharing"
echo ""
echo -e "${BLUE}🎯 Ready for Production Media Chat!${NC}"
