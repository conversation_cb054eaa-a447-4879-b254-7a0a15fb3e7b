<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Yupcha Admin Dashboard - Workflow Tester</title>
    <style>
        :root {
            --blue: #0d6efd; --green: #198754; --yellow: #ffc107; --red: #dc3545;
            --gray-100: #f8f9fa; --gray-200: #e9ecef; --gray-500: #adb5bd; --gray-700: #495057;
        }
        body { font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif; margin: 0; padding: 20px; background-color: var(--gray-100); color: var(--gray-700); }
        .container { max-width: 1200px; margin: 0 auto; }
        h1, h2, h3 { color: #333; border-bottom: 1px solid #eee; padding-bottom: 10px; margin-top: 0; }
        .panel { background: white; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.08); padding: 20px; margin-bottom: 20px; }
        .grid-container { display: grid; grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); gap: 20px; }
        .form-section { display: flex; flex-direction: column; gap: 10px; }
        .form-section input, .form-section select, .form-section textarea { padding: 10px; border: 1px solid #ccc; border-radius: 4px; font-size: 1rem; }
        .form-section textarea { resize: vertical; min-height: 80px; }
        button { padding: 10px 15px; border: none; border-radius: 4px; cursor: pointer; color: white; background-color: var(--blue); font-size: 1rem; transition: background-color 0.2s; }
        button:hover { opacity: 0.9; }
        button:disabled { background-color: var(--gray-500); cursor: not-allowed; }
        .login-btn { background-color: var(--green); }
        .logout-btn { background-color: var(--red); }
        .status-bar { padding: 15px; margin-bottom: 20px; border-radius: 4px; background-color: var(--gray-200); }
        .results-area { background-color: #212529; color: #f8f9fa; padding: 15px; border-radius: 4px; font-family: monospace; white-space: pre-wrap; word-wrap: break-word; max-height: 400px; overflow-y: auto; }
        .success { background-color: #d1edff; color: #0c5460; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .error { background-color: #f8d7da; color: #721c24; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .hidden { display: none; }
        .workflow-section { margin-top: 20px; }
        .step-indicator { display: inline-block; background: var(--blue); color: white; padding: 5px 10px; border-radius: 15px; font-size: 0.8rem; margin-right: 10px; }
    </style>
</head>
<body>

<div class="container">
    <h1>🚀 Yupcha Admin Workflow Tester</h1>

    <div id="authPanel" class="panel">
        <h2>1. Authentication</h2>
        <div id="loginForm" class="form-section">
            <input type="email" id="loginEmail" placeholder="Email" value="<EMAIL>">
            <input type="password" id="loginPassword" placeholder="Password" value="adminpassword">
            <button class="login-btn" onclick="login()">Login</button>
        </div>
        <div id="userInfo" class="hidden">
            <div class="status-bar">Logged in as: <strong id="userName"></strong> (<span id="userRole"></span>)</div>
            <button class="logout-btn" onclick="logout()">Logout</button>
        </div>
    </div>

    <div id="adminActions" class="panel hidden">
        <h2>2. Admin Actions</h2>
        <div class="grid-container">
            <div class="form-section">
                <h3>Create Organization</h3>
                <input type="text" id="orgName" placeholder="Organization Name" value="Test Organization">
                <textarea id="orgDescription" placeholder="Description">A test organization for demo</textarea>
                <input type="url" id="orgWebsite" placeholder="Website" value="https://test.com">
                <input type="email" id="orgEmail" placeholder="Contact Email" value="<EMAIL>">
                <input type="tel" id="orgPhone" placeholder="Phone" value="+1234567890">
                <input type="text" id="orgAddress" placeholder="Address" value="123 Test Street">
                <button onclick="createOrganization()">Create Organization</button>
            </div>
            <div class="form-section">
                <h3>Create Team</h3>
                <select id="teamOrgSelect"></select>
                <input type="text" id="teamName" placeholder="Team Name" value="Support Team">
                <textarea id="teamDescription" placeholder="Team Description">Customer support team</textarea>
                <button onclick="createTeam()">Create Team</button>
            </div>
            <div class="form-section">
                <h3>Create Agent</h3>
                <select id="agentOrgSelect"></select>
                <select id="agentTeamSelect"></select>
                <input type="email" id="agentEmail" placeholder="Agent Email" value="<EMAIL>">
                <input type="text" id="agentFullName" placeholder="Agent Full Name" value="Agent Two">
                <input type="password" id="agentPassword" placeholder="Agent Initial Password" value="agent2password">
                <button onclick="createAgent()">Create Agent</button>
            </div>
        </div>
    </div>

    <div id="workflowSection" class="panel workflow-section hidden">
        <h2>3. Complete Workflow Test</h2>
        <div class="form-section">
            <h3>Create Customer & Conversation</h3>
            <input type="text" id="customerName" placeholder="Customer Name" value="John Doe">
            <input type="email" id="customerEmail" placeholder="Customer Email" value="<EMAIL>">
            <input type="tel" id="customerPhone" placeholder="Customer Phone" value="+1234567890">
            <input type="text" id="customerLocation" placeholder="Location" value="New York, USA">
            <button onclick="runCompleteWorkflow()">Run Complete Workflow</button>
        </div>
    </div>

    <div id="resultsPanel" class="panel hidden">
        <h2>4. View Data</h2>
        <div class="grid-container">
            <button onclick="fetchOrganizations()">View Organizations</button>
            <button onclick="fetchTeams()">View Teams</button>
            <button onclick="fetchUsers()">View All Users</button>
            <button onclick="fetchCustomers()">View Customers</button>
            <button onclick="fetchConversations()">View Conversations</button>
        </div>
        <h3>Results:</h3>
        <div id="resultsArea" class="results-area">Click a button to view data...</div>
    </div>
</div>

<script>
    const API_BASE_URL = "http://localhost:8000/api";
    let loggedInUser = null;

    function showResults(data) { document.getElementById('resultsArea').textContent = JSON.stringify(data, null, 2); }

    function updateUIForLoginState(isLoggedIn) {
        document.getElementById('loginForm').classList.toggle('hidden', isLoggedIn);
        document.getElementById('userInfo').classList.toggle('hidden', !isLoggedIn);
        
        const showAdminTools = isLoggedIn && loggedInUser && loggedInUser.role === 'admin';
        document.getElementById('adminActions').classList.toggle('hidden', !showAdminTools);
        document.getElementById('resultsPanel').classList.toggle('hidden', !isLoggedIn);

        if (isLoggedIn) {
            document.getElementById('userName').textContent = loggedInUser.full_name;
            document.getElementById('userRole').textContent = loggedInUser.role;
        }
    }


    function showMessage(message, type = 'info') {
        const resultsArea = document.getElementById('resultsArea');
        const timestamp = new Date().toLocaleTimeString();
        const logEntry = `[${timestamp}] ${message}\n${resultsArea.textContent}`;
        resultsArea.textContent = logEntry;
    }

    function toggleAdminActions(show) {
        document.getElementById('adminActions').classList.toggle('hidden', !show);
        document.getElementById('workflowSection').classList.toggle('hidden', !show);
        document.getElementById('resultsPanel').classList.toggle('hidden', !show);
    }

    // --- AUTHENTICATION ---
    async function login() {
        const email = document.getElementById('loginEmail').value;
        const password = document.getElementById('loginPassword').value;
        try {
            const response = await fetch(`${API_BASE_URL}/auth/login`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                body: `username=${encodeURIComponent(email)}&password=${encodeURIComponent(password)}`,
                credentials: 'include'
            });
            
            const data = await response.text();
            if (response.ok && data.includes('Login successful')) {
                showMessage("✅ Login successful", 'success');
                await checkLoginStatus();
            } else {
                throw new Error(`Login failed: ${data}`);
            }
        } catch (error) {
            alert(`Error: ${error.message}`);
            showMessage(`❌ Login failed: ${error.message}`, 'error');
        }
    }

    async function checkLoginStatus() {
        try {
            const response = await fetch(`${API_BASE_URL}/auth/me`);
            if (!response.ok) {
                // This means the cookie is invalid or not present
                loggedInUser = null;
                updateUIForLoginState(false);
                return;
            }
            loggedInUser = await response.json();
            updateUIForLoginState(true);

            // If admin is logged in, populate the data
            if (loggedInUser.role === 'admin') {
                await populateSelects();
            }
        } catch (error) {
            console.log("Not logged in:", error);
            updateUIForLoginState(false);
        }
    }


    async function logout() {
        try {
            await fetch(`${API_BASE_URL}/auth/logout`, { 
                method: 'POST',
                credentials: 'include'
            });
        } catch (error) {
            console.log("Logout error:", error);
        }
        
        loggedInUser = null;
        document.getElementById('loginForm').classList.remove('hidden');
        document.getElementById('userInfo').classList.add('hidden');
        toggleAdminActions(false);
        showResults("Logged out.");
    }

    // --- DATA CREATION ---
    async function createOrganization() {
        const name = document.getElementById('orgName').value;
        const description = document.getElementById('orgDescription').value;
        const website = document.getElementById('orgWebsite').value;
        const email = document.getElementById('orgEmail').value;
        const phone = document.getElementById('orgPhone').value;
        const address = document.getElementById('orgAddress').value;

        if (!name) return alert('Organization name is required.');
        
        try {
            const orgData = {
                name: name,
                description: description || 'Created from demo',
                website: website || null,
                email: email || null,
                phone: phone || null,
                address: address || null
            };

            const response = await fetch(`${API_BASE_URL}/organizations/`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(orgData),
                credentials: 'include'
            });
            
            const data = await response.json();
            if (!response.ok) throw new Error(data.detail || 'Failed to create organization');
            
            showResults(data);
            showMessage(`✅ Organization created: ID ${data.id}`, 'success');
            await populateSelects();
        } catch (error) { 
            alert(`Error: ${error.message}`);
            showMessage(`❌ Organization creation failed: ${error.message}`, 'error');
        }
    }

    async function createTeam() {
        const orgId = document.getElementById('teamOrgSelect').value;
        const name = document.getElementById('teamName').value;
        const description = document.getElementById('teamDescription').value;
        
        if (!orgId || !name) return alert('Organization and Team Name are required.');
        
        try {
            const teamData = {
                name: name,
                description: description || 'Created from demo',
                organization_id: parseInt(orgId)
            };

            const response = await fetch(`${API_BASE_URL}/teams/`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(teamData),
                credentials: 'include'
            });
            
            const data = await response.json();
            if (!response.ok) throw new Error(data.detail || 'Failed to create team');
            
            showResults(data);
            showMessage(`✅ Team created: ID ${data.id}`, 'success');
            await populateSelects();
        } catch (error) { 
            alert(`Error: ${error.message}`);
            showMessage(`❌ Team creation failed: ${error.message}`, 'error');
        }
    }

    async function createAgent() {
        const orgId = document.getElementById('agentOrgSelect').value;
        const teamId = document.getElementById('agentTeamSelect').value;
        const email = document.getElementById('agentEmail').value;
        const fullName = document.getElementById('agentFullName').value;
        const password = document.getElementById('agentPassword').value;

        if (!orgId || !teamId || !email || !fullName || !password) {
            return alert('All agent fields are required.');
        }
        
        try {
            const agentData = {
                email,
                full_name: fullName,
                password,
                role: 'agent',
                organization_id: parseInt(orgId),
                team_id: parseInt(teamId)
            };

            const response = await fetch(`${API_BASE_URL}/users/`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(agentData),
                credentials: 'include'
            });
            
            const data = await response.json();
            if (!response.ok) throw new Error(data.detail || 'Failed to create agent');
            
            showResults(data);
            showMessage(`✅ Agent created: ID ${data.id}`, 'success');
            alert(`Agent created successfully! \nEmail: ${email}\nPassword: ${password}`);
        } catch (error) { 
            alert(`Error: ${error.message}`);
            showMessage(`❌ Agent creation failed: ${error.message}`, 'error');
        }
    }

    // --- COMPLETE WORKFLOW ---
    async function runCompleteWorkflow() {
        showMessage("🚀 Starting complete workflow test...");
        
        try {
            // Step 1: Create Customer
            const timestamp = Date.now();
            const customerData = {
                customer_id: `cust-${timestamp}`,
                name: document.getElementById('customerName').value || 'John Doe',
                email: document.getElementById('customerEmail').value || `john${timestamp}@example.com`,
                phone: document.getElementById('customerPhone').value || '+1234567890',
                ip_address: '*************',
                location: document.getElementById('customerLocation').value || 'New York, USA'
            };

            showMessage("📝 Step 1: Creating customer...");
            const customerResponse = await fetch(`${API_BASE_URL}/customers/`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(customerData)
            });

            let customerDbId;
            if (customerResponse.ok) {
                const customer = await customerResponse.json();
                customerDbId = customer.id;
                showMessage(`✅ Customer created: ID ${customerDbId}`);
            } else {
                // Try to get existing customer
                const existingCustomers = await fetch(`${API_BASE_URL}/customers/`, {
                    credentials: 'include'
                });
                if (existingCustomers.ok) {
                    const customers = await existingCustomers.json();
                    if (customers.length > 0) {
                        customerDbId = customers[0].id;
                        showMessage(`ℹ️ Using existing customer: ID ${customerDbId}`);
                    } else {
                        throw new Error('No customers available');
                    }
                } else {
                    throw new Error('Failed to create or find customer');
                }
            }

            // Step 2: Create Conversation
            showMessage("💬 Step 2: Creating conversation...");
            const orgId = document.getElementById('agentOrgSelect').value;
            if (!orgId) throw new Error('No organization selected');

            const convData = {
                customer_id: customerDbId,
                organization_id: parseInt(orgId)
            };

            const convResponse = await fetch(`${API_BASE_URL}/conversations/`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(convData)
            });

            if (!convResponse.ok) throw new Error('Failed to create conversation');
            const conversation = await convResponse.json();
            showMessage(`✅ Conversation created: ID ${conversation.id}`);

            // Step 3: Assign Team
            const teamId = document.getElementById('agentTeamSelect').value;
            if (teamId) {
                showMessage("🔗 Step 3: Assigning team to conversation...");
                const assignResponse = await fetch(`${API_BASE_URL}/conversations/${conversation.id}/assign/${teamId}`, {
                    method: 'POST',
                    credentials: 'include'
                });

                if (assignResponse.ok) {
                    const assignedConv = await assignResponse.json();
                    showMessage(`✅ Team assigned successfully. Status: ${assignedConv.status}`);
                } else {
                    showMessage(`⚠️ Team assignment failed`);
                }
            }

            showMessage("🎉 Complete workflow test finished!");
            showResults({
                workflow_completed: true,
                customer_id: customerDbId,
                conversation_id: conversation.id,
                team_assigned: !!teamId
            });

        } catch (error) {
            showMessage(`❌ Workflow failed: ${error.message}`, 'error');
            alert(`Workflow Error: ${error.message}`);
        }
    }

    // --- DATA FETCHING & UI ---
    async function fetchOrganizations() {
        try {
            const response = await fetch(`${API_BASE_URL}/organizations/`, {
                credentials: 'include'
            });
            if (!response.ok) throw new Error('Failed to fetch organizations');
            const data = await response.json();
            showResults(data);
            return data;
        } catch (e) { 
            alert(e.message);
            showMessage(`❌ Fetch organizations failed: ${e.message}`, 'error');
            return []; 
        }
    }

    async function fetchTeams() {
        try {
            const response = await fetch(`${API_BASE_URL}/teams/`, {
                credentials: 'include'
            });
            if (!response.ok) throw new Error('Failed to fetch teams');
            const data = await response.json();
            showResults(data);
            return data;
        } catch(e) { 
            alert(e.message);
            showMessage(`❌ Fetch teams failed: ${e.message}`, 'error');
            return []; 
        }
    }

    async function fetchUsers() {
        try {
            const response = await fetch(`${API_BASE_URL}/users/`, {
                credentials: 'include'
            });
            if (!response.ok) throw new Error('Failed to fetch users');
            const data = await response.json();
            showResults(data);
        } catch (e) { 
            alert(e.message);
            showMessage(`❌ Fetch users failed: ${e.message}`, 'error');
        }
    }

    async function fetchCustomers() {
        try {
            const response = await fetch(`${API_BASE_URL}/customers/`, {
                credentials: 'include'
            });
            if (!response.ok) throw new Error('Failed to fetch customers');
            const data = await response.json();
            showResults(data);
        } catch (e) { 
            alert(e.message);
            showMessage(`❌ Fetch customers failed: ${e.message}`, 'error');
        }
    }

    async function fetchConversations() {
        try {
            const response = await fetch(`${API_BASE_URL}/conversations/`, {
                credentials: 'include'
            });
            if (!response.ok) throw new Error('Failed to fetch conversations');
            const data = await response.json();
            showResults(data);
        } catch (e) { 
            alert(e.message);
            showMessage(`❌ Fetch conversations failed: ${e.message}`, 'error');
        }
    }
    
    async function populateSelects() {
        try {
            const orgs = await fetchOrganizations();
            const teams = await fetchTeams();
            
            const teamOrgSelect = document.getElementById('teamOrgSelect');
            const agentOrgSelect = document.getElementById('agentOrgSelect');
            const agentTeamSelect = document.getElementById('agentTeamSelect');

            // Clear all selects
            [teamOrgSelect, agentOrgSelect, agentTeamSelect].forEach(sel => sel.innerHTML = '');

            if (orgs.length === 0) {
                teamOrgSelect.innerHTML = '<option value="">Create an organization first</option>';
                agentOrgSelect.innerHTML = '<option value="">Create an organization first</option>';
            } else {
                orgs.forEach(org => {
                    teamOrgSelect.innerHTML += `<option value="${org.id}">${org.name}</option>`;
                    agentOrgSelect.innerHTML += `<option value="${org.id}">${org.name}</option>`;
                });
            }

            if (teams.length === 0) {
                agentTeamSelect.innerHTML = '<option value="">Create a team first</option>';
            } else {
                teams.forEach(team => {
                    agentTeamSelect.innerHTML += `<option value="${team.id}">${team.name} (Org ID: ${team.organization_id})</option>`;
                });
            }
        } catch (error) {
            console.error("Failed to populate dropdowns:", error);
            showMessage(`❌ Failed to populate dropdowns: ${error.message}`, 'error');
        }
    }

    // Initial check on page load
    window.onload = checkLoginStatus;
</script>

</body>
</html>