import asyncio
import websockets
import json
import time
import random
import uuid

# --- Configuration ---
# You can change these values to test different scenarios
WS_BASE_URL = "ws://localhost:8000/api/ws"
CONVERSATION_ID = 1  # We now have real conversations in the DB!
ORGANIZATION_ID = 1 # The organization this conversation belongs to

# Generate a unique customer ID for each run, or use a static one to test a returning customer
# For testing with existing customers, use: "cust_001", "cust_002", "cust_003"
CUSTOMER_ID = f"test-customer-{uuid.uuid4().hex[:8]}"
# CUSTOMER_ID = "cust_001"  # Uncomment to use existing test customer

# List of sample messages the "customer" will send
SAMPLE_MESSAGES = [
    "Hello? Is anyone there?",
    "I have a question about my recent order.",
    "My order number is YUP-12345.",
    "The item arrived damaged.",
    "Can I get a replacement?",
    "What is the process for returns?",
    "I need help urgently.",
    "Okay, I will wait.",
    "Thank you for the information.",
    "This is very frustrating.",
    "Can you please connect me to a human agent?",
]

async def customer_chat_spammer():
    """
    Simulates a customer connecting to a conversation and sending messages
    at random intervals.
    """
    uri = f"{WS_BASE_URL}/chat/{CONVERSATION_ID}?customer_id={CUSTOMER_ID}"
    
    print("==================================================")
    print("🚀 Yupcha Customer Chat Client Tester")
    print("==================================================")
    print(f"👤 Customer ID: {CUSTOMER_ID}")
    print(f"💬 Conversation ID: {CONVERSATION_ID}")
    print(f"🔗 Connecting to: {uri}")
    print("--------------------------------------------------")

    try:
        async with websockets.connect(uri) as websocket:
            print("✅ Connection successful! Starting to send messages...")
            
            # A task to listen for incoming messages (like from the bot or an agent)
            async def listen_for_messages():
                try:
                    async for message in websocket:
                        data = json.loads(message)
                        msg_type = data.get('type', 'unknown')
                        sender = data.get('sender', 'system')
                        content = data.get('content', data.get('detail', ''))
                        timestamp = data.get('timestamp', '')

                        if msg_type == 'message':
                            print(f"📨 [{sender.upper()}]: {content}")
                            if timestamp:
                                print(f"   ⏰ {timestamp}")
                        else:
                            print(f"🔔 System: {content}")
                except websockets.ConnectionClosed:
                    print("👂 Listener: Connection closed.")
            
            listener_task = asyncio.create_task(listen_for_messages())
            
            # The main loop for sending messages
            message_count = 0
            while True:
                # Choose a random message and send it
                message_to_send = random.choice(SAMPLE_MESSAGES)
                payload = {
                    "content": message_to_send,
                    "sender": "customer", # This is set by the client, but the backend verifies the connection type
                    "message_type": "text"
                }
                
                await websocket.send(json.dumps(payload))
                message_count += 1
                print(f"📤 Sent (Msg #{message_count}): {message_to_send}")

                # Wait for a random interval before sending the next message
                sleep_time = random.uniform(3, 8) # Wait between 3 and 8 seconds
                print(f"⏳ Waiting {sleep_time:.1f}s before next message...")
                await asyncio.sleep(sleep_time)

    except websockets.exceptions.ConnectionClosedError as e:
        print(f"❌ Connection closed with error: {e.code} {e.reason}")
    except Exception as e:
        print(f"❌ An unexpected error occurred: {e}")
    finally:
        print("--------------------------------------------------")
        print("⏹️ Script finished.")

if __name__ == "__main__":
    try:
        asyncio.run(customer_chat_spammer())
    except KeyboardInterrupt:
        print("\n👋 Script interrupted by user.")