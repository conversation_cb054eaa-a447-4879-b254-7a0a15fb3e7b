import './assets/main.css'
import { createApp } from 'vue'
import { createRouter, createWebHistory } from 'vue-router'
import axios from 'axios'
import App from './App.vue'

// Import components
import LoginComponent from './components/Login.vue'
import DashboardComponent from './components/Dashboard.vue'
import ConversationsComponent from './components/Conversations.vue'
import CustomersComponent from './components/Customers.vue'
import TeamsComponent from './components/Teams.vue'
import OrganizationsComponent from './components/Organizations.vue'
import UsersComponent from './components/Users.vue'
import ChatComponent from './components/Chat.vue'

// API Configuration
const API_BASE_URL = 'http://localhost:8000'
axios.defaults.baseURL = API_BASE_URL
axios.defaults.withCredentials = true

// Router configuration
const routes = [
    { path: '/', redirect: '/login' },
    { path: '/login', component: LoginComponent, meta: { requiresGuest: true } },
    { path: '/dashboard', component: DashboardComponent, meta: { requiresAuth: true } },
    { path: '/conversations', name: 'Conversations', component: ConversationsComponent, meta: { requiresAuth: true } },
    { path: '/conversations/:id', name: 'Chat', component: ChatComponent, meta: { requiresAuth: true } },
    { path: '/customers', component: CustomersComponent, meta: { requiresAuth: true } },
    { path: '/teams', component: TeamsComponent, meta: { requiresAuth: true, requiresAdmin: true } },
    { path: '/organizations', component: OrganizationsComponent, meta: { requiresAuth: true, requiresAdmin: true } },
    { path: '/users', component: UsersComponent, meta: { requiresAuth: true, requiresAdmin: true } }
]

const router = createRouter({
    history: createWebHistory(),
    routes
})

// Create Vue app
const app = createApp(App)

// Global properties
app.config.globalProperties.$api = axios
app.config.globalProperties.$API_BASE_URL = API_BASE_URL

// Use router
app.use(router)

// Mount the app
app.mount('#app')
