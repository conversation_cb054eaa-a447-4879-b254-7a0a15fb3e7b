<template>
  <div class="dashboard">
    <div class="dashboard-header">
      <h1><i class="fas fa-tachometer-alt"></i> Dashboard</h1>
      <p>Welcome back, {{ user?.full_name }}! Here's your overview.</p>
    </div>

    <!-- Stats Cards -->
    <div class="stats-grid">
      <div class="stat-card">
        <div class="stat-icon">
          <i class="fas fa-comments"></i>
        </div>
        <div class="stat-content">
          <h3>{{ stats.totalConversations }}</h3>
          <p>Total Conversations</p>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-icon">
          <i class="fas fa-clock"></i>
        </div>
        <div class="stat-content">
          <h3>{{ stats.activeConversations }}</h3>
          <p>Active Conversations</p>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-icon">
          <i class="fas fa-users"></i>
        </div>
        <div class="stat-content">
          <h3>{{ stats.totalCustomers }}</h3>
          <p>Total Customers</p>
        </div>
      </div>

      <div class="stat-card" v-if="user?.role === 'admin'">
        <div class="stat-icon">
          <i class="fas fa-user-friends"></i>
        </div>
        <div class="stat-content">
          <h3>{{ stats.totalTeams }}</h3>
          <p>Active Teams</p>
        </div>
      </div>
    </div>

    <!-- Quick Actions -->
    <div class="quick-actions">
      <h2><i class="fas fa-bolt"></i> Quick Actions</h2>
      <div class="actions-grid">
        <router-link to="/conversations" class="action-card">
          <i class="fas fa-comments"></i>
          <h3>View Conversations</h3>
          <p>Manage customer conversations</p>
        </router-link>

        <router-link to="/customers" class="action-card">
          <i class="fas fa-user-plus"></i>
          <h3>Add Customer</h3>
          <p>Create new customer record</p>
        </router-link>

        <router-link to="/teams" class="action-card" v-if="user?.role === 'admin'">
          <i class="fas fa-users-cog"></i>
          <h3>Manage Teams</h3>
          <p>Configure teams and agents</p>
        </router-link>

        <router-link to="/organizations" class="action-card" v-if="user?.role === 'admin'">
          <i class="fas fa-building"></i>
          <h3>Organizations</h3>
          <p>Manage organizations</p>
        </router-link>
      </div>
    </div>

    <!-- Recent Activity -->
    <div class="recent-activity">
      <div class="card">
        <div class="card-header">
          <h2><i class="fas fa-history"></i> Recent Activity</h2>
          <button @click="refreshData" class="btn btn-sm btn-secondary">
            <i class="fas fa-sync-alt" :class="{ 'fa-spin': loading }"></i>
            Refresh
          </button>
        </div>

        <div v-if="loading" class="loading-state">
          <i class="fas fa-spinner fa-spin"></i>
          <p>Loading recent activity...</p>
        </div>

        <div v-else-if="recentConversations.length === 0" class="empty-state">
          <i class="fas fa-inbox"></i>
          <p>No recent conversations</p>
        </div>

        <div v-else class="activity-list">
          <div 
            v-for="conversation in recentConversations" 
            :key="conversation.id"
            class="activity-item"
            @click="viewConversation(conversation.id)"
          >
            <div class="activity-icon">
              <i class="fas fa-comment"></i>
            </div>
            <div class="activity-content">
              <h4>{{ conversation.customer?.name || 'Unknown Customer' }}</h4>
              <p>{{ conversation.customer?.email || 'No email' }}</p>
              <span class="activity-time">
                {{ formatTime(conversation.created_at) }}
              </span>
            </div>
            <div class="activity-status">
              <span :class="['badge', `badge-${getStatusColor(conversation.status)}`]">
                {{ conversation.status }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted, getCurrentInstance } from 'vue'
import { useRouter } from 'vue-router'
import axios from 'axios'

export default {
  name: 'Dashboard',
  setup() {
    const router = useRouter()
    const app = getCurrentInstance()
    const globalState = app.appContext.config.globalProperties.$globalState
    const showToast = app.appContext.config.globalProperties.$showToast
    
    const loading = ref(false)
    const user = computed(() => globalState.user)
    
    const stats = reactive({
      totalConversations: 0,
      activeConversations: 0,
      totalCustomers: 0,
      totalTeams: 0
    })
    
    const recentConversations = ref([])
    
    const loadDashboardData = async () => {
      loading.value = true
      try {
        // Load conversations
        const conversationsResponse = await axios.get('/api/conversations/')
        const conversations = conversationsResponse.data
        stats.totalConversations = conversations.length
        stats.activeConversations = conversations.filter(c => c.status === 'active').length
        
        // Get recent conversations (last 10)
        recentConversations.value = conversations
          .sort((a, b) => new Date(b.created_at) - new Date(a.created_at))
          .slice(0, 10)
        
        // Load customers
        const customersResponse = await axios.get('/api/customers/')
        stats.totalCustomers = customersResponse.data.length
        
        // Load teams (admin only)
        if (user.value?.role === 'admin') {
          const teamsResponse = await axios.get('/api/teams/')
          stats.totalTeams = teamsResponse.data.length
        }
        
      } catch (error) {
        console.error('Error loading dashboard data:', error)
        if (error.response?.status === 401) {
          showToast('Session expired. Please login again.', 'error')
          // Redirect to login if unauthorized
          router.push('/login')
        } else {
          showToast(`Failed to load dashboard data: ${error.response?.data?.detail || error.message}`, 'error')
        }
      } finally {
        loading.value = false
      }
    }
    
    const refreshData = () => {
      loadDashboardData()
    }
    
    const viewConversation = (conversationId) => {
      router.push(`/conversations/${conversationId}`)
    }
    
    const formatTime = (timestamp) => {
      const date = new Date(timestamp)
      const now = new Date()
      const diff = now - date
      
      if (diff < 60000) return 'Just now'
      if (diff < 3600000) return `${Math.floor(diff / 60000)}m ago`
      if (diff < 86400000) return `${Math.floor(diff / 3600000)}h ago`
      return `${Math.floor(diff / 86400000)}d ago`
    }
    
    const getStatusColor = (status) => {
      const colors = {
        'new': 'info',
        'active': 'success',
        'pending': 'warning',
        'resolved': 'secondary',
        'closed': 'secondary'
      }
      return colors[status] || 'secondary'
    }
    
    onMounted(() => {
      loadDashboardData()
    })
    
    return {
      loading,
      user,
      stats,
      recentConversations,
      refreshData,
      viewConversation,
      formatTime,
      getStatusColor
    }
  }
}
</script>

<style scoped>
.dashboard-header {
  margin-bottom: 30px;
}

.dashboard-header h1 {
  color: var(--dark-color);
  margin-bottom: 5px;
}

.dashboard-header p {
  color: var(--secondary-color);
  margin: 0;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: var(--shadow);
  display: flex;
  align-items: center;
  gap: 15px;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  background: var(--primary-color);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
}

.stat-content h3 {
  font-size: 2rem;
  font-weight: bold;
  margin: 0 0 5px 0;
  color: var(--dark-color);
}

.stat-content p {
  margin: 0;
  color: var(--secondary-color);
  font-size: 0.9rem;
}

.quick-actions {
  margin-bottom: 30px;
}

.quick-actions h2 {
  margin-bottom: 20px;
  color: var(--dark-color);
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.action-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: var(--shadow);
  text-decoration: none;
  color: var(--dark-color);
  transition: all 0.3s ease;
  text-align: center;
}

.action-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
  color: var(--primary-color);
}

.action-card i {
  font-size: 2rem;
  color: var(--primary-color);
  margin-bottom: 10px;
}

.action-card h3 {
  margin: 0 0 5px 0;
  font-size: 1.1rem;
}

.action-card p {
  margin: 0;
  color: var(--secondary-color);
  font-size: 0.9rem;
}

.activity-list {
  max-height: 400px;
  overflow-y: auto;
}

.activity-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px;
  border-bottom: 1px solid var(--border-color);
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.activity-item:hover {
  background-color: var(--light-color);
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background: var(--info-color);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.activity-content {
  flex: 1;
}

.activity-content h4 {
  margin: 0 0 2px 0;
  font-size: 0.9rem;
  color: var(--dark-color);
}

.activity-content p {
  margin: 0 0 2px 0;
  font-size: 0.8rem;
  color: var(--secondary-color);
}

.activity-time {
  font-size: 0.75rem;
  color: var(--secondary-color);
}

.loading-state,
.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: var(--secondary-color);
}

.loading-state i,
.empty-state i {
  font-size: 2rem;
  margin-bottom: 10px;
  display: block;
}

.badge {
  display: inline-block;
  padding: 4px 8px;
  font-size: 0.75rem;
  font-weight: 500;
  border-radius: 4px;
  text-transform: uppercase;
}

.badge-success {
  background: var(--success-color);
  color: white;
}

.badge-warning {
  background: var(--warning-color);
  color: var(--dark-color);
}

.badge-info {
  background: var(--info-color);
  color: white;
}

.badge-secondary {
  background: var(--secondary-color);
  color: white;
}

@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .actions-grid {
    grid-template-columns: 1fr;
  }
}
</style>
