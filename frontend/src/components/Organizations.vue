<template>
  <div class="organizations">
    <div class="page-header">
      <div class="header-content">
        <h1><i class="fas fa-building"></i> Organizations</h1>
        <p>Manage organizations and their settings</p>
      </div>
      <div class="header-actions">
        <button @click="refreshOrganizations" class="btn btn-secondary">
          <i class="fas fa-sync-alt" :class="{ 'fa-spin': loading }"></i>
          Refresh
        </button>
        <button @click="openCreateModal" class="btn btn-primary">
          <i class="fas fa-plus"></i>
          Create Organization
        </button>
      </div>
    </div>

    <div v-if="loading" class="loading-state">
      <i class="fas fa-spinner fa-spin"></i>
      <p>Loading organizations...</p>
    </div>

    <div v-else-if="organizations.length === 0" class="empty-state">
      <i class="fas fa-building"></i>
      <p>No organizations found</p>
    </div>

    <div v-else class="organizations-grid">
      <div v-for="org in organizations" :key="org.id" class="org-card">
        <div class="org-header">
          <div class="org-info">
            <h3>{{ org.name }}</h3>
            <p>{{ org.description }}</p>
          </div>
          <div class="org-actions">
            <button @click="editOrganization(org)" class="btn btn-sm btn-secondary">
              <i class="fas fa-edit"></i>
            </button>
            <button @click="deleteOrganization(org)" class="btn btn-sm btn-danger">
              <i class="fas fa-trash"></i>
            </button>
          </div>
        </div>

        <div class="org-stats">
          <div class="stat-item">
            <i class="fas fa-user-friends"></i>
            <span>0 Teams</span>
          </div>
          <div class="stat-item">
            <i class="fas fa-comments"></i>
            <span>0 Conversations</span>
          </div>
          <div class="stat-item">
            <i class="fas fa-users"></i>
            <span>0 Users</span>
          </div>
        </div>

        <div class="org-details" v-if="org.website || org.contact_email">
          <div class="detail-row" v-if="org.website">
            <i class="fas fa-globe"></i>
            <a :href="org.website" target="_blank">{{ org.website }}</a>
          </div>
          <div class="detail-row" v-if="org.contact_email">
            <i class="fas fa-envelope"></i>
            <span>{{ org.contact_email }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Create/Edit Organization Modal -->
    <div v-if="showModal" class="modal-overlay" @click.self="closeModal">
      <div class="modal-content">
        <form @submit.prevent="saveOrganization" class="form-section">
          <h3>
            <i :class="isEditing ? 'fas fa-edit' : 'fas fa-plus'"></i>
            {{ isEditing ? 'Edit Organization' : 'Create New Organization' }}
          </h3>

          <div class="form-group">
            <label for="orgName">Organization Name *</label>
            <input
              type="text"
              id="orgName"
              v-model="orgForm.name"
              placeholder="e.g., Acme Corporation"
              required
            >
          </div>

          <div class="form-group">
            <label for="orgDescription">Description</label>
            <textarea
              id="orgDescription"
              v-model="orgForm.description"
              rows="3"
              placeholder="Brief description of the organization"
            ></textarea>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label for="orgWebsite">Website</label>
              <input
                type="url"
                id="orgWebsite"
                v-model="orgForm.website"
                placeholder="https://example.com"
              >
            </div>

            <div class="form-group">
              <label for="orgEmail">Contact Email</label>
              <input
                type="email"
                id="orgEmail"
                v-model="orgForm.contact_email"
                placeholder="<EMAIL>"
              >
            </div>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label for="orgPhone">Phone</label>
              <input
                type="tel"
                id="orgPhone"
                v-model="orgForm.phone"
                placeholder="+1234567890"
              >
            </div>
          </div>

          <div class="form-group">
            <label for="orgAddress">Address</label>
            <textarea
              id="orgAddress"
              v-model="orgForm.address"
              rows="2"
              placeholder="Full address"
            ></textarea>
          </div>

          <div class="modal-actions">
            <button type="button" class="btn btn-secondary" @click="closeModal">Cancel</button>
            <button type="submit" class="btn btn-primary" :disabled="isSubmitting">
              <i v-if="isSubmitting" class="fas fa-spinner fa-spin"></i>
              <i v-else :class="isEditing ? 'fas fa-save' : 'fas fa-plus'"></i>
              {{ isSubmitting ? 'Saving...' : (isEditing ? 'Save Changes' : 'Create Organization') }}
            </button>
          </div>

          <div v-if="modalError" class="error-message">{{ modalError }}</div>
        </form>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, onMounted, getCurrentInstance } from 'vue'
import axios from 'axios'

export default {
  name: 'Organizations',
  setup() {
    const app = getCurrentInstance()
    const showToast = app.appContext.config.globalProperties.$showToast

    const loading = ref(false)
    const organizations = ref([])

    const showModal = ref(false)
    const isEditing = ref(false)
    const isSubmitting = ref(false)
    const modalError = ref('')

    const orgForm = reactive({
      id: null,
      name: '',
      description: '',
      website: '',
      contact_email: '',
      phone: '',
      address: ''
    })

    const loadOrganizations = async () => {
      loading.value = true
      try {
        const response = await axios.get('/api/organizations/')
        organizations.value = response.data
      } catch (error) {
        console.error('Error loading organizations:', error)
        showToast('Failed to load organizations', 'error')
      } finally {
        loading.value = false
      }
    }

    const refreshOrganizations = () => {
      loadOrganizations()
    }

    const resetForm = () => {
      orgForm.id = null
      orgForm.name = ''
      orgForm.description = ''
      orgForm.website = ''
      orgForm.contact_email = ''
      orgForm.phone = ''
      orgForm.address = ''
    }

    const openCreateModal = () => {
      resetForm()
      isEditing.value = false
      modalError.value = ''
      showModal.value = true
    }

    const editOrganization = (org) => {
      resetForm()
      orgForm.id = org.id
      orgForm.name = org.name
      orgForm.description = org.description || ''
      orgForm.website = org.website || ''
      orgForm.contact_email = org.contact_email || ''
      orgForm.phone = org.phone || ''
      orgForm.address = org.address || ''
      isEditing.value = true
      modalError.value = ''
      showModal.value = true
    }

    const closeModal = () => {
      showModal.value = false
      resetForm()
    }

    const saveOrganization = async () => {
      isSubmitting.value = true
      modalError.value = ''

      try {
        const orgData = {
          name: orgForm.name,
          description: orgForm.description || null,
          website: orgForm.website || null,
          contact_email: orgForm.contact_email || null,
          phone: orgForm.phone || null,
          address: orgForm.address || null
        }

        if (isEditing.value) {
          await axios.put(`/api/organizations/${orgForm.id}`, orgData)
          showToast('Organization updated successfully', 'success')
        } else {
          await axios.post('/api/organizations/', orgData)
          showToast('Organization created successfully', 'success')
        }

        closeModal()
        await loadOrganizations()

      } catch (error) {
        console.error('Error saving organization:', error)
        modalError.value = error.response?.data?.detail || 'Failed to save organization'
      } finally {
        isSubmitting.value = false
      }
    }

    const deleteOrganization = async (org) => {
      if (!confirm(`Are you sure you want to delete organization "${org.name}"?`)) {
        return
      }

      try {
        await axios.delete(`/api/organizations/${org.id}`)
        showToast('Organization deleted successfully', 'success')
        await loadOrganizations()
      } catch (error) {
        console.error('Error deleting organization:', error)
        showToast('Failed to delete organization', 'error')
      }
    }

    onMounted(() => {
      loadOrganizations()
    })

    return {
      loading,
      organizations,
      showModal,
      isEditing,
      isSubmitting,
      modalError,
      orgForm,
      refreshOrganizations,
      openCreateModal,
      editOrganization,
      closeModal,
      saveOrganization,
      deleteOrganization
    }
  }
}
</script>

<style scoped>
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 30px;
  gap: 20px;
}

.header-content h1 {
  color: var(--dark-color);
  margin-bottom: 5px;
}

.header-content p {
  color: var(--secondary-color);
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 10px;
  flex-shrink: 0;
}

.organizations-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 20px;
}

.org-card {
  background: white;
  border-radius: 8px;
  box-shadow: var(--shadow);
  padding: 20px;
}

.org-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 15px;
}

.org-info h3 {
  margin: 0 0 5px 0;
  color: var(--dark-color);
}

.org-info p {
  margin: 0;
  color: var(--secondary-color);
  font-size: 0.9rem;
}

.org-actions {
  display: flex;
  gap: 5px;
}

.org-stats {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid var(--border-color);
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 0.9rem;
  color: var(--secondary-color);
}

.org-details {
  margin-bottom: 20px;
}

.detail-row {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  font-size: 0.9rem;
  color: var(--secondary-color);
}

.detail-row a {
  color: var(--primary-color);
  text-decoration: none;
}

.detail-row a:hover {
  text-decoration: underline;
}

.loading-state,
.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: var(--secondary-color);
}

.loading-state i,
.empty-state i {
  font-size: 2rem;
  margin-bottom: 10px;
  display: block;
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: stretch;
    gap: 15px;
  }
  
  .header-actions {
    justify-content: flex-end;
  }
  
  .organizations-grid {
    grid-template-columns: 1fr;
  }
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  padding: 30px;
  border-radius: 8px;
  width: 100%;
  max-width: 600px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  max-height: 90vh;
  overflow-y: auto;
}

.form-section h3 {
  margin-bottom: 20px;
  color: var(--dark-color);
  display: flex;
  align-items: center;
  gap: 10px;
}

.form-group {
  margin-bottom: 20px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: var(--dark-color);
}

.form-group input,
.form-group textarea,
.form-group select {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  font-size: 0.9rem;
  transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
}

.error-message {
  background: #f8d7da;
  color: #721c24;
  padding: 12px;
  border-radius: 6px;
  margin-top: 15px;
  font-size: 0.9rem;
}

@media (max-width: 768px) {
  .form-row {
    grid-template-columns: 1fr;
  }
}
</style>
