<template>
  <div class="users">
    <!-- Header with working "Add User" button -->
    <div class="page-header">
      <div class="header-content">
        <h1><i class="fas fa-user-cog"></i> Users</h1>
        <p>Manage system users and their permissions</p>
      </div>
      <div class="header-actions">
        <button @click="loadUsers" class="btn btn-secondary" :disabled="loading">
          <i class="fas fa-sync-alt" :class="{ 'fa-spin': loading }"></i>
          Refresh
        </button>
        <button @click="openAddUserModal" class="btn btn-primary">
          <i class="fas fa-user-plus"></i>
          Add User
        </button>
      </div>
    </div>

    <!-- User List Card -->
    <div class="card">
      <div class="card-header">
        <h3 class="card-title">Users ({{ users.length }})</h3>
      </div>

      <div v-if="loading" class="loading-state">
        <i class="fas fa-spinner fa-spin fa-2x"></i>
        <p>Loading users...</p>
      </div>

      <div v-else-if="users.length === 0" class="empty-state">
        <i class="fas fa-user-slash fa-2x"></i>
        <p>No users found</p>
      </div>

      <div v-else class="table-responsive">
        <table class="table">
          <thead>
            <tr>
              <th>User</th>
              <th>Role</th>
              <th>Team</th>
              <th>Status</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="user in users" :key="user.id">
              <td>
                <div class="user-info">
                  <div class="user-avatar">
                    <i class="fas fa-user"></i>
                  </div>
                  <div>
                    <strong>{{ user.full_name }}</strong>
                    <br>
                    <small class="text-muted">{{ user.email }}</small>
                  </div>
                </div>
              </td>
              <td>
                <span :class="['badge', `badge-${getRoleColor(user.role)}`]">
                  {{ user.role }}
                </span>
              </td>
              <td>
                <span v-if="user.team" class="team-badge">
                  <i class="fas fa-users"></i>
                  {{ user.team.name }}
                </span>
                <span v-else class="text-muted">
                  <i class="fas fa-minus"></i>
                  Unassigned
                </span>
              </td>
              <td>
                <span :class="['badge', user.is_active ? 'badge-success' : 'badge-secondary']">
                  {{ user.is_active ? 'Active' : 'Inactive' }}
                </span>
              </td>
              <td>
                <div class="action-buttons">
                  <button @click="editUser(user)" class="btn btn-sm btn-secondary" title="Edit User">
                    <i class="fas fa-edit"></i>
                  </button>
                  <button @click="toggleUserStatus(user)"
                    :class="['btn', 'btn-sm', user.is_active ? 'btn-warning' : 'btn-success']"
                    :title="user.is_active ? 'Deactivate User' : 'Activate User'">
                    <i :class="user.is_active ? 'fas fa-user-slash' : 'fas fa-user-check'"></i>
                  </button>
                  <button @click="deleteUser(user)" class="btn btn-sm btn-danger" title="Delete User">
                    <i class="fas fa-trash"></i>
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- Add User Modal -->
    <div v-if="showModal" class="modal-overlay" @click.self="closeAddUserModal">
      <div class="modal-content">
        <form @submit.prevent="handleAddUser" class="form-section">
          <h3>
            <i :class="isEditing ? 'fas fa-user-edit' : 'fas fa-user-plus'"></i>
            {{ isEditing ? 'Edit User' : 'Add New User' }}
          </h3>
          <div class="form-row">
            <div class="form-group">
              <label for="fullName">
                <i class="fas fa-user"></i>
                Full Name *
              </label>
              <input type="text" id="fullName" v-model="newUser.full_name" placeholder="John Doe" required>
            </div>

            <div class="form-group">
              <label for="email">
                <i class="fas fa-envelope"></i>
                Email Address *
              </label>
              <input type="email" id="email" v-model="newUser.email" placeholder="<EMAIL>" required :disabled="isEditing">
            </div>
          </div>

          <div class="form-group" v-if="!isEditing">
            <label for="password">
              <i class="fas fa-lock"></i>
              Initial Password *
            </label>
            <input type="password" id="password" v-model="newUser.password" placeholder="Enter secure password" required>
          </div>
          <div class="form-row">
            <div class="form-group">
              <label for="role">
                <i class="fas fa-user-tag"></i>
                Role *
              </label>
              <select id="role" v-model="newUser.role" required>
                <option value="agent">Agent</option>
                <option value="admin">Admin</option>
              </select>
            </div>

            <div class="form-group">
              <label for="status">
                <i class="fas fa-toggle-on"></i>
                Status
              </label>
              <select id="status" v-model="newUser.is_active">
                <option :value="true">Active</option>
                <option :value="false">Inactive</option>
              </select>
            </div>
          </div>
          <div class="form-row">
            <div class="form-group">
              <label for="organization">
                <i class="fas fa-building"></i>
                Organization *
              </label>
              <select id="organization" v-model.number="newUser.organization_id" required>
                <option value="">Select Organization</option>
                <option v-for="org in organizations" :key="org.id" :value="org.id">{{ org.name }}</option>
              </select>
            </div>

            <div class="form-group">
              <label for="team">
                <i class="fas fa-users"></i>
                Team
              </label>
              <select id="team" v-model.number="newUser.team_id">
                <option :value="null">-- Unassigned --</option>
                <option v-for="team in teams" :key="team.id" :value="team.id">{{ team.name }}</option>
              </select>
            </div>
          </div>
          <div class="modal-actions">
            <button type="button" class="btn btn-secondary" @click="closeAddUserModal">Cancel</button>
            <button type="submit" class="btn btn-primary" :disabled="isSubmitting">
              <i v-if="isSubmitting" class="fas fa-spinner fa-spin"></i>
              <i v-else :class="isEditing ? 'fas fa-save' : 'fas fa-user-plus'"></i>
              {{ isSubmitting ? (isEditing ? 'Updating...' : 'Creating...') : (isEditing ? 'Update User' : 'Create User') }}
            </button>
          </div>
          <div v-if="modalError" class="error-message">{{ modalError }}</div>
        </form>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, onMounted, getCurrentInstance } from 'vue'
import axios from 'axios'

export default {
  name: 'Users',
  setup() {
    const app = getCurrentInstance()
    const showToast = app.appContext.config.globalProperties.$showToast
    
    const loading = ref(true)
    const users = ref([])
    
    const showModal = ref(false)
    const isSubmitting = ref(false)
    const isEditing = ref(false)
    const modalError = ref('')
    const organizations = ref([])
    const teams = ref([])
    const newUser = reactive({
      id: null,
      full_name: '',
      email: '',
      password: '',
      role: 'agent',
      organization_id: null,
      team_id: null,
      is_active: true
    })

    const loadUsers = async () => {
      loading.value = true
      try {
        const response = await axios.get('/api/users/')
        users.value = response.data
      } catch (error) {
        showToast('Failed to load users', 'error')
      } finally {
        loading.value = false
      }
    }
    
    const openAddUserModal = async () => {
      Object.assign(newUser, { full_name: '', email: '', password: '', role: 'agent', organization_id: null, team_id: null });
      modalError.value = '';
      try {
        const [orgResponse, teamResponse] = await Promise.all([
          axios.get('/api/organizations/'),
          axios.get('/api/teams/')
        ]);
        organizations.value = orgResponse.data;
        teams.value = teamResponse.data;
        if (organizations.value.length > 0) newUser.organization_id = organizations.value[0].id;
        showModal.value = true;
      } catch (error) {
        showToast('Could not load data for form.', 'error');
      }
    }

    const closeAddUserModal = () => {
      showModal.value = false;
      isEditing.value = false;
      // Reset form
      newUser.id = null;
      newUser.full_name = '';
      newUser.email = '';
      newUser.password = '';
      newUser.role = 'agent';
      newUser.organization_id = null;
      newUser.team_id = null;
      newUser.is_active = true;
      modalError.value = '';
    }

    const handleAddUser = async () => {
      isSubmitting.value = true
      modalError.value = ''
      try {
        const payload = { ...newUser };
        if (!payload.team_id) delete payload.team_id;

        if (isEditing.value) {
          // Update existing user
          const updatePayload = { ...payload };
          delete updatePayload.id;
          delete updatePayload.password; // Don't update password in edit mode
          await axios.put(`/api/users/${newUser.id}`, updatePayload);
          showToast('User updated successfully!', 'success');
        } else {
          // Create new user
          await axios.post('/api/users/', payload)
          showToast('User created successfully!', 'success')
        }

        closeAddUserModal()
        await loadUsers()
      } catch (error) {
        modalError.value = error.response?.data?.detail || `Failed to ${isEditing.value ? 'update' : 'create'} user.`
      } finally {
        isSubmitting.value = false
      }
    }
    
    const getRoleColor = (role) => ({ 'admin': 'danger', 'agent': 'primary' }[role] || 'secondary')

    const editUser = (user) => {
      // Fill form with user data
      newUser.full_name = user.full_name
      newUser.email = user.email
      newUser.role = user.role
      newUser.team_id = user.team_id || ''
      newUser.organization_id = user.organization_id || ''
      newUser.is_active = user.is_active
      newUser.id = user.id

      isEditing.value = true
      showModal.value = true
    }

    const toggleUserStatus = async (user) => {
      try {
        await axios.patch(`/api/users/${user.id}`, { is_active: !user.is_active })
        showToast(`User ${user.is_active ? 'deactivated' : 'activated'} successfully`, 'success')
        await loadUsers()
      } catch (error) {
        showToast('Failed to update user status', 'error')
      }
    }

    const deleteUser = async (user) => {
      if (!confirm(`Are you sure you want to delete user "${user.full_name}"?`)) {
        return
      }

      try {
        await axios.delete(`/api/users/${user.id}`)
        showToast('User deleted successfully', 'success')
        await loadUsers()
      } catch (error) {
        showToast('Failed to delete user', 'error')
      }
    }

    onMounted(loadUsers)

    return {
      loading, users, loadUsers, getRoleColor,
      showModal, isSubmitting, isEditing, modalError, newUser, organizations, teams,
      openAddUserModal, closeAddUserModal, handleAddUser,
      editUser, toggleUserStatus, deleteUser
    }
  }
}
</script>


<style scoped>
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 30px;
  gap: 20px;
}

.header-content h1 {
  color: var(--dark-color);
  margin-bottom: 5px;
}

.header-content p {
  color: var(--secondary-color);
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 10px;
  flex-shrink: 0;
}

.table-responsive {
  overflow-x: auto;
}

.table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 20px;
}

.table th,
.table td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid var(--border-color);
}

.table th {
  background-color: var(--light-color);
  font-weight: 600;
  color: var(--dark-color);
}

.table tbody tr:hover {
  background-color: #f8f9fa;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: var(--primary-color);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.2rem;
}

.action-buttons {
  display: flex;
  gap: 5px;
}

.team-badge {
  display: inline-flex;
  align-items: center;
  gap: 5px;
  background: var(--light-color);
  color: var(--secondary-color);
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
}

.badge {
  display: inline-block;
  padding: 4px 8px;
  font-size: 0.75rem;
  font-weight: 500;
  border-radius: 4px;
  text-transform: uppercase;
}

.badge-success {
  background: var(--success-color);
  color: white;
}

.badge-danger {
  background: var(--danger-color);
  color: white;
}

.badge-primary {
  background: var(--primary-color);
  color: white;
}

.badge-secondary {
  background: var(--secondary-color);
  color: white;
}

.text-muted {
  color: var(--secondary-color);
}

.loading-state,
.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: var(--secondary-color);
}

.loading-state i,
.empty-state i {
  font-size: 2rem;
  margin-bottom: 10px;
  display: block;
}

  .modal-overlay {
    position: fixed; top: 0; left: 0; width: 100%; height: 100%;
    background-color: rgba(0,0,0,0.6); display: flex;
    align-items: center; justify-content: center; z-index: 1000;
  }
  .modal-content {
    background: white; padding: 30px; border-radius: 8px;
    width: 100%; max-width: 500px; box-shadow: 0 5px 15px rgba(0,0,0,0.3);
  }
  .modal-actions {
    display: flex; justify-content: flex-end; gap: 10px; margin-top: 20px;
  }  
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: stretch;
    gap: 15px;
  }
  
  .header-actions {
    justify-content: flex-end;
  }
  
  .table {
    font-size: 0.8rem;
  }
}

/* Form Styling */
.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.form-group label {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  font-weight: 500;
  color: var(--dark-color);
}

.form-group label i {
  color: var(--primary-color);
  width: 16px;
}

.form-group input,
.form-group select {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  font-size: 0.9rem;
  transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.error-message {
  background: #f8d7da;
  color: #721c24;
  padding: 12px;
  border-radius: 6px;
  margin-top: 15px;
  font-size: 0.9rem;
}

@media (max-width: 768px) {
  .form-row {
    grid-template-columns: 1fr;
  }
}
</style>
