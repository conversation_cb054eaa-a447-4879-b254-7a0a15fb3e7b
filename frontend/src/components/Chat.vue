<template>
  <div class="chat-container">
    <div class="chat-header">
      <h2 class="chat-title">💬 Customer Support Chat</h2>
      <div class="connection-status" :class="{ connected: isConnected }">
        <div class="status-dot"></div>
        <span>{{ isConnected ? 'Connected' : 'Disconnected' }}</span>
      </div>
    </div>

    <div class="chat-layout">
      <!-- Conversations List -->
      <div class="conversations-sidebar">
        <div class="sidebar-header">
          <h3>Active Conversations</h3>
          <button @click="loadConversations" class="btn btn-sm btn-secondary">
            <i class="fas fa-sync-alt"></i>
          </button>
        </div>

        <div class="conversations-list">
          <div
            v-for="conversation in conversations"
            :key="conversation.id"
            class="conversation-item"
            :class="{ active: selectedConversation?.id === conversation.id }"
            @click="selectConversation(conversation)"
          >
            <div class="conversation-info">
              <div class="customer-name">
                {{ conversation.customer?.name || `Customer #${conversation.customer?.id}` }}
              </div>
              <div class="last-message">
                {{ conversation.lastMessage || 'No messages yet' }}
              </div>
              <div class="conversation-time">
                {{ formatTime(conversation.updated_at) }}
              </div>
            </div>
            <div class="conversation-status" :class="conversation.status">
              {{ conversation.status }}
            </div>
          </div>
        </div>
      </div>

      <!-- Chat Area -->
      <div class="chat-area">
        <div v-if="!selectedConversation" class="no-conversation">
          <i class="fas fa-comments"></i>
          <h3>Select a conversation to start chatting</h3>
          <p>Choose a conversation from the sidebar to view messages and respond to customers.</p>
        </div>

        <div v-else class="chat-content">
          <!-- Chat Header -->
          <div class="chat-info">
            <div class="customer-details">
              <h4>{{ selectedConversation.customer?.name || 'Unknown Customer' }}</h4>
              <p>{{ selectedConversation.customer?.email || 'No email' }}</p>
              <span class="customer-id">ID: {{ selectedConversation.customer?.customer_id }}</span>
            </div>
            <div class="chat-actions">
              <button @click="connectToChat" :disabled="isConnected" class="btn btn-primary btn-sm">
                <i class="fas fa-plug"></i> Connect
              </button>
              <button @click="disconnectFromChat" :disabled="!isConnected" class="btn btn-secondary btn-sm">
                <i class="fas fa-times"></i> Disconnect
              </button>
            </div>
          </div>

          <!-- Messages Area -->
          <div class="messages-container" ref="messagesContainer">
            <div class="messages-list">
              <div
                v-for="message in messages"
                :key="message.id"
                class="message"
                :class="message.sender"
              >
                <div class="message-content">
                  <div class="message-text">{{ message.content }}</div>
                  <div class="message-meta">
                    <span class="sender">{{ message.sender }}</span>
                    <span class="time">{{ formatTime(message.created_at) }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Message Input -->
          <div class="message-input-area">
            <div class="input-container">
              <textarea
                v-model="newMessage"
                @keydown.enter.prevent="sendMessage"
                placeholder="Type your message here... (Press Enter to send)"
                class="message-input"
                rows="3"
              ></textarea>
              <button @click="sendMessage" :disabled="!newMessage.trim() || !isConnected" class="send-button">
                <i class="fas fa-paper-plane"></i>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, onUnmounted, nextTick } from 'vue'

export default {
  name: 'Chat',
  setup() {
    // Reactive data
    const conversations = ref([])
    const selectedConversation = ref(null)
    const messages = ref([])
    const newMessage = ref('')
    const isConnected = ref(false)
    const websocket = ref(null)
    const messagesContainer = ref(null)

    // Methods
    const loadConversations = async () => {
      try {
        const response = await fetch('/api/conversations/', {
          credentials: 'include' // Use session cookies
        })

        if (response.ok) {
          conversations.value = await response.json()
          console.log('✅ Loaded conversations:', conversations.value.length)
        } else {
          console.error('❌ Failed to load conversations')
        }
      } catch (error) {
        console.error('❌ Error loading conversations:', error)
      }
    }

    const selectConversation = async (conversation) => {
      selectedConversation.value = conversation
      await loadMessages(conversation.id)

      // Disconnect from previous chat if connected
      if (isConnected.value) {
        disconnectFromChat()
      }
    }

    const loadMessages = async (conversationId) => {
      try {
        const response = await fetch(`/api/conversations/${conversationId}/messages`, {
          credentials: 'include' // Use session cookies
        })

        if (response.ok) {
          messages.value = await response.json()
          console.log('✅ Loaded messages:', messages.value.length)
          await nextTick()
          scrollToBottom()
        } else {
          console.error('❌ Failed to load messages')
          messages.value = []
        }
      } catch (error) {
        console.error('❌ Error loading messages:', error)
        messages.value = []
      }
    }

    const connectToChat = () => {
      if (!selectedConversation.value) return

      // Use WebSocket without token - session cookies will be sent automatically
      const wsUrl = `ws://localhost:8000/api/ws/chat/${selectedConversation.value.id}`
      console.log('🔌 Connecting to:', wsUrl)

      websocket.value = new WebSocket(wsUrl)

      websocket.value.onopen = () => {
        isConnected.value = true
        console.log('✅ Connected to chat WebSocket')
      }

      websocket.value.onmessage = (event) => {
        try {
          const message = JSON.parse(event.data)
          console.log('📨 Received message:', message)

          // Add message to the list
          messages.value.push(message)
          nextTick(() => scrollToBottom())

        } catch (error) {
          console.error('❌ Error parsing message:', error)
        }
      }

      websocket.value.onclose = () => {
        isConnected.value = false
        console.log('🔌 Disconnected from chat WebSocket')
      }

      websocket.value.onerror = (error) => {
        console.error('❌ WebSocket error:', error)
      }
    }

    const disconnectFromChat = () => {
      if (websocket.value) {
        websocket.value.close()
        websocket.value = null
      }
    }

    const sendMessage = () => {
      if (!newMessage.value.trim() || !isConnected.value || !websocket.value) return

      const message = {
        content: newMessage.value.trim(),
        sender: 'agent',
        message_type: 'text'
      }

      websocket.value.send(JSON.stringify(message))
      newMessage.value = ''
    }

    const scrollToBottom = () => {
      if (messagesContainer.value) {
        messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
      }
    }

    const formatTime = (timestamp) => {
      if (!timestamp) return ''
      return new Date(timestamp).toLocaleTimeString()
    }

    // Lifecycle
    onMounted(() => {
      loadConversations()
    })

    onUnmounted(() => {
      disconnectFromChat()
    })

    return {
      conversations,
      selectedConversation,
      messages,
      newMessage,
      isConnected,
      messagesContainer,
      loadConversations,
      selectConversation,
      connectToChat,
      disconnectFromChat,
      sendMessage,
      formatTime
    }
  }
}
</script>

<style scoped>
.chat-container {
  height: calc(100vh - 100px);
  display: flex;
  flex-direction: column;
}

.chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid var(--border-light);
  background: var(--bg-card);
}

.chat-title {
  margin: 0;
  color: var(--text-primary);
}

.connection-status {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--danger-color);
}

.connection-status.connected {
  color: var(--success-color);
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: currentColor;
}

.chat-layout {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.conversations-sidebar {
  width: 300px;
  border-right: 1px solid var(--border-light);
  background: var(--bg-secondary);
  display: flex;
  flex-direction: column;
}

.sidebar-header {
  padding: 15px;
  border-bottom: 1px solid var(--border-light);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.sidebar-header h3 {
  margin: 0;
  color: var(--text-primary);
  font-size: 1rem;
}

.conversations-list {
  flex: 1;
  overflow-y: auto;
}

.conversation-item {
  padding: 15px;
  border-bottom: 1px solid var(--border-light);
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.conversation-item:hover {
  background: var(--bg-hover);
}

.conversation-item.active {
  background: var(--primary-color);
  color: white;
}

.conversation-info {
  margin-bottom: 8px;
}

.customer-name {
  font-weight: 600;
  margin-bottom: 4px;
}

.last-message {
  font-size: 0.9rem;
  opacity: 0.8;
  margin-bottom: 4px;
}

.conversation-time {
  font-size: 0.8rem;
  opacity: 0.7;
}

.conversation-status {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
}

.conversation-status.active {
  background: var(--success-color);
  color: white;
}

.conversation-status.closed {
  background: var(--secondary-color);
  color: white;
}

.chat-area {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.no-conversation {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: var(--text-muted);
  text-align: center;
}

.no-conversation i {
  font-size: 4rem;
  margin-bottom: 20px;
}

.chat-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.chat-info {
  padding: 15px 20px;
  border-bottom: 1px solid var(--border-light);
  background: var(--bg-card);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.customer-details h4 {
  margin: 0 0 4px 0;
  color: var(--text-primary);
}

.customer-details p {
  margin: 0 0 4px 0;
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.customer-id {
  font-size: 0.8rem;
  color: var(--text-muted);
}

.chat-actions {
  display: flex;
  gap: 8px;
}

.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
}

.message {
  margin-bottom: 15px;
  display: flex;
}

.message.customer .message-content {
  background: var(--bg-tertiary);
  margin-left: 0;
  margin-right: auto;
}

.message.agent .message-content {
  background: var(--primary-color);
  color: white;
  margin-left: auto;
  margin-right: 0;
}

.message.bot .message-content {
  background: var(--secondary-color);
  color: white;
  margin-left: 0;
  margin-right: auto;
}

.message-content {
  max-width: 70%;
  padding: 12px 16px;
  border-radius: 18px;
}

.message-text {
  margin-bottom: 4px;
}

.message-meta {
  font-size: 0.8rem;
  opacity: 0.8;
  display: flex;
  justify-content: space-between;
}

.message-input-area {
  padding: 20px;
  border-top: 1px solid var(--border-light);
  background: var(--bg-card);
}

.input-container {
  display: flex;
  gap: 10px;
  align-items: flex-end;
}

.message-input {
  flex: 1;
  resize: none;
  border: 1px solid var(--border-light);
  border-radius: 20px;
  padding: 12px 16px;
  background: var(--bg-input);
  color: var(--text-primary);
}

.message-input:focus {
  outline: none;
  border-color: var(--primary-color);
}

.send-button {
  background: var(--primary-color);
  color: white;
  border: none;
  border-radius: 50%;
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.send-button:hover:not(:disabled) {
  background: var(--primary-color);
  opacity: 0.9;
}

.send-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}
</style>
