<template>
  <div class="chat-container">
    <div class="chat-header">
      <h2>Conversation #{{ conversationId }}</h2>
      <p v-if="conversation">
        With Customer: <strong>{{ conversation.customer?.name || 'Unknown' }}</strong>
      </p>
      <div class="status" :class="wsStatus">{{ wsStatus }}</div>
    </div>

    <div class="messages-container" ref="messagesContainer">
      <div v-for="message in messages" :key="message.id" class="message-wrapper">
        <div class="message" :class="getMessageClass(message.sender)">
          <div class="sender-info">{{ message.sender }}</div>
          <div class="content">{{ message.content }}</div>
          <div class="timestamp">{{ formatTime(message.created_at) }}</div>
        </div>
      </div>
    </div>

    <div class="message-input">
      <form @submit.prevent="sendMessage" class="input-form">
        <input v-model="newMessage" type="text" class="form-control" placeholder="Type your message...">
        <button type="submit" class="btn btn-primary" :disabled="!isWsConnected">
          <i class="fas fa-paper-plane"></i> Send
        </button>
      </form>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, onUnmounted, nextTick, computed } from 'vue'
import { useRoute } from 'vue-router'
import axios from 'axios'

export default {
  name: 'Chat',
  setup() {
    const route = useRoute();
    const conversationId = route.params.id;
    
    const conversation = ref(null);
    const messages = ref([]);
    const newMessage = ref('');
    const ws = ref(null);
    const wsStatus = ref('Disconnected');
    const messagesContainer = ref(null);

    const isWsConnected = computed(() => ws.value && ws.value.readyState === WebSocket.OPEN);

    const scrollToBottom = () => {
      nextTick(() => {
        if (messagesContainer.value) {
          messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight;
        }
      });
    };

    const loadInitialData = async () => {
      try {
        const [convResponse, messagesResponse] = await Promise.all([
          axios.get(`/api/conversations/${conversationId}`),
          axios.get(`/api/conversations/${conversationId}/messages`)
        ]);
        conversation.value = convResponse.data;
        messages.value = messagesResponse.data;
        scrollToBottom();
      } catch (error) {
        console.error("Failed to load chat data", error);
      }
    };

    const connectWebSocket = () => {
      const wsUrl = `ws://localhost:8000/api/ws/chat/${conversationId}`;
      ws.value = new WebSocket(wsUrl);

      ws.value.onopen = () => { wsStatus.value = 'Connected'; };
      ws.value.onclose = () => { wsStatus.value = 'Disconnected'; };
      ws.value.onerror = () => { wsStatus.value = 'Error'; };

      ws.value.onmessage = (event) => {
        const data = JSON.parse(event.data);
        if (data.type === 'message') {
          messages.value.push(data);
          scrollToBottom();
        }
      };
    };

    const sendMessage = () => {
      if (!newMessage.value.trim() || !isWsConnected.value) return;
      
      const payload = {
        content: newMessage.value,
        type: 'message', // Although the backend defaults this, it's good practice to be explicit
      };
      ws.value.send(JSON.stringify(payload));
      newMessage.value = '';
    };

    const getMessageClass = (sender) => {
        if (sender === 'customer') return 'customer-message';
        if (sender === 'agent' || sender === 'admin') return 'agent-message';
        return 'system-message';
    };

    onMounted(async () => {
      await loadInitialData();
      connectWebSocket();
    });

    onUnmounted(() => {
      if (ws.value) {
        ws.value.close();
      }
    });

    return {
      conversationId,
      conversation,
      messages,
      newMessage,
      wsStatus,
      isWsConnected,
      messagesContainer,
      sendMessage,
      getMessageClass,
      formatTime: (ts) => new Date(ts).toLocaleTimeString()
    };
  }
}
</script>

<style scoped>

.message-wrapper { display: flex; flex-direction: column; }
.message { display: inline-block; }
.customer-message { align-self: flex-end; background-color: var(--blue); color: white; }
.agent-message { align-self: flex-start; background-color: var(--green); color: white; }
.system-message { align-self: center; background-color: var(--gray-200); font-style: italic; }
.timestamp { font-size: 0.7rem; color: var(--gray-500); margin-top: 4px; }
.customer-message .timestamp { text-align: right; }

.chat-container {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 100px);
  background: white;
  border-radius: 8px;
  box-shadow: var(--shadow);
  overflow: hidden;
}

.chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid var(--border-color);
  background: var(--light-color);
}

.chat-info {
  display: flex;
  align-items: center;
  gap: 15px;
}

.conversation-details h2 {
  margin: 0 0 5px 0;
  font-size: 1.2rem;
  color: var(--dark-color);
}

.conversation-details p {
  margin: 0;
  color: var(--secondary-color);
  font-size: 0.9rem;
}

.chat-body {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: hidden;
}

.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
}

.empty-messages {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--secondary-color);
  text-align: center;
}

.empty-messages i {
  font-size: 3rem;
  margin-bottom: 15px;
}

.message-input {
  padding: 20px;
  border-top: 1px solid var(--border-color);
  background: white;
}

.input-form {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.input-group {
  display: flex;
  gap: 10px;
}

.input-group .form-control {
  flex: 1;
}

@media (max-width: 768px) {
  .chat-header {
    flex-direction: column;
    align-items: stretch;
    gap: 15px;
  }
  
  .chat-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
}
</style>
