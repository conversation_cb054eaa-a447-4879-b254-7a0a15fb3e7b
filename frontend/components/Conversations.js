const ConversationsComponent = {
    template: `
        <div class="conversations">
            <div class="page-header">
                <div class="header-content">
                    <h1><i class="fas fa-comments"></i> Conversations</h1>
                    <p>Manage customer conversations and support tickets</p>
                </div>
                <div class="header-actions">
                    <button @click="refreshConversations" class="btn btn-secondary">
                        <i class="fas fa-sync-alt" :class="{ 'fa-spin': loading }"></i>
                        Refresh
                    </button>
                    <button @click="showCreateModal = true" class="btn btn-primary">
                        <i class="fas fa-plus"></i>
                        New Conversation
                    </button>
                </div>
            </div>

            <!-- Filters -->
            <div class="filters-card card">
                <div class="filters-grid">
                    <div class="filter-group">
                        <label class="form-label">Status</label>
                        <select v-model="filters.status" @change="applyFilters" class="form-control form-select">
                            <option value="">All Statuses</option>
                            <option value="new">New</option>
                            <option value="active">Active</option>
                            <option value="pending">Pending</option>
                            <option value="resolved">Resolved</option>
                            <option value="closed">Closed</option>
                        </select>
                    </div>
                    
                    <div class="filter-group">
                        <label class="form-label">Team</label>
                        <select v-model="filters.team" @change="applyFilters" class="form-control form-select">
                            <option value="">All Teams</option>
                            <option value="unassigned">Unassigned</option>
                            <option v-for="team in teams" :key="team.id" :value="team.id">
                                {{ team.name }}
                            </option>
                        </select>
                    </div>
                    
                    <div class="filter-group">
                        <label class="form-label">Search</label>
                        <input 
                            v-model="filters.search" 
                            @input="applyFilters"
                            type="text" 
                            class="form-control" 
                            placeholder="Search customers..."
                        >
                    </div>
                </div>
            </div>

            <!-- Conversations List -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        Conversations ({{ filteredConversations.length }})
                    </h3>
                </div>

                <div v-if="loading" class="loading-state">
                    <i class="fas fa-spinner fa-spin"></i>
                    <p>Loading conversations...</p>
                </div>

                <div v-else-if="filteredConversations.length === 0" class="empty-state">
                    <i class="fas fa-inbox"></i>
                    <p>No conversations found</p>
                    <button @click="showCreateModal = true" class="btn btn-primary">
                        <i class="fas fa-plus"></i>
                        Create First Conversation
                    </button>
                </div>

                <div v-else class="conversations-list">
                    <div 
                        v-for="conversation in filteredConversations" 
                        :key="conversation.id"
                        class="conversation-item"
                        @click="openConversation(conversation.id)"
                    >
                        <div class="conversation-avatar">
                            <i class="fas fa-user"></i>
                        </div>
                        
                        <div class="conversation-content">
                            <div class="conversation-header">
                                <h4>{{ conversation.customer?.name || 'Unknown Customer' }}</h4>
                                <span class="conversation-time">
                                    {{ formatTime(conversation.created_at) }}
                                </span>
                            </div>
                            
                            <div class="conversation-details">
                                <p class="customer-email">{{ conversation.customer?.email || 'No email' }}</p>
                                <div class="conversation-meta">
                                    <span :class="['badge', \`badge-\${getStatusColor(conversation.status)}\`]">
                                        {{ conversation.status }}
                                    </span>
                                    <span v-if="conversation.team" class="team-badge">
                                        <i class="fas fa-users"></i>
                                        {{ conversation.team.name }}
                                    </span>
                                    <span v-else class="team-badge unassigned">
                                        <i class="fas fa-exclamation-triangle"></i>
                                        Unassigned
                                    </span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="conversation-actions">
                            <button 
                                @click.stop="assignTeam(conversation)"
                                class="btn btn-sm btn-secondary"
                                v-if="!conversation.team_id && user?.role === 'admin'"
                            >
                                <i class="fas fa-user-plus"></i>
                                Assign
                            </button>
                            
                            <button 
                                @click.stop="openConversation(conversation.id)"
                                class="btn btn-sm btn-primary"
                            >
                                <i class="fas fa-eye"></i>
                                View
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Create Conversation Modal -->
            <div v-if="showCreateModal" class="modal-overlay" @click="showCreateModal = false">
                <div class="modal" @click.stop>
                    <div class="modal-header">
                        <h3><i class="fas fa-plus"></i> Create New Conversation</h3>
                        <button @click="showCreateModal = false" class="modal-close">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    
                    <form @submit.prevent="createConversation" class="modal-body">
                        <div class="form-group">
                            <label class="form-label">Customer</label>
                            <select v-model="newConversation.customer_id" class="form-control form-select" required>
                                <option value="">Select a customer</option>
                                <option v-for="customer in customers" :key="customer.id" :value="customer.id">
                                    {{ customer.name }} ({{ customer.email }})
                                </option>
                            </select>
                        </div>
                        
                        <div class="form-group" v-if="user?.role === 'admin'">
                            <label class="form-label">Organization</label>
                            <select v-model="newConversation.organization_id" class="form-control form-select" required>
                                <option value="">Select an organization</option>
                                <option v-for="org in organizations" :key="org.id" :value="org.id">
                                    {{ org.name }}
                                </option>
                            </select>
                        </div>
                        
                        <div class="modal-footer">
                            <button type="button" @click="showCreateModal = false" class="btn btn-secondary">
                                Cancel
                            </button>
                            <button type="submit" class="btn btn-primary" :disabled="creatingConversation">
                                <i v-if="creatingConversation" class="fas fa-spinner fa-spin"></i>
                                <i v-else class="fas fa-plus"></i>
                                {{ creatingConversation ? 'Creating...' : 'Create Conversation' }}
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Assign Team Modal -->
            <div v-if="showAssignModal" class="modal-overlay" @click="showAssignModal = false">
                <div class="modal" @click.stop>
                    <div class="modal-header">
                        <h3><i class="fas fa-user-plus"></i> Assign Team</h3>
                        <button @click="showAssignModal = false" class="modal-close">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    
                    <form @submit.prevent="assignTeamToConversation" class="modal-body">
                        <div class="form-group">
                            <label class="form-label">Select Team</label>
                            <select v-model="assignData.team_id" class="form-control form-select" required>
                                <option value="">Choose a team</option>
                                <option v-for="team in teams" :key="team.id" :value="team.id">
                                    {{ team.name }} ({{ team.description }})
                                </option>
                            </select>
                        </div>
                        
                        <div class="modal-footer">
                            <button type="button" @click="showAssignModal = false" class="btn btn-secondary">
                                Cancel
                            </button>
                            <button type="submit" class="btn btn-primary" :disabled="assigningTeam">
                                <i v-if="assigningTeam" class="fas fa-spinner fa-spin"></i>
                                <i v-else class="fas fa-check"></i>
                                {{ assigningTeam ? 'Assigning...' : 'Assign Team' }}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    `,
    
    setup() {
        const { ref, reactive, computed, onMounted } = Vue;
        const router = VueRouter.useRouter();
        
        const loading = ref(false);
        const creatingConversation = ref(false);
        const assigningTeam = ref(false);
        const showCreateModal = ref(false);
        const showAssignModal = ref(false);
        
        const user = computed(() => globalState.user);
        
        const conversations = ref([]);
        const customers = ref([]);
        const teams = ref([]);
        const organizations = ref([]);
        
        const filters = reactive({
            status: '',
            team: '',
            search: ''
        });
        
        const newConversation = reactive({
            customer_id: '',
            organization_id: ''
        });
        
        const assignData = reactive({
            conversation_id: null,
            team_id: ''
        });
        
        const filteredConversations = computed(() => {
            let filtered = conversations.value;
            
            if (filters.status) {
                filtered = filtered.filter(c => c.status === filters.status);
            }
            
            if (filters.team) {
                if (filters.team === 'unassigned') {
                    filtered = filtered.filter(c => !c.team_id);
                } else {
                    filtered = filtered.filter(c => c.team_id == filters.team);
                }
            }
            
            if (filters.search) {
                const search = filters.search.toLowerCase();
                filtered = filtered.filter(c => 
                    c.customer?.name?.toLowerCase().includes(search) ||
                    c.customer?.email?.toLowerCase().includes(search)
                );
            }
            
            return filtered.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));
        });
        
        const loadData = async () => {
            loading.value = true;
            try {
                const [conversationsData, customersData, teamsData, organizationsData] = await Promise.all([
                    api.getConversations(),
                    api.getCustomers(),
                    api.getTeams(),
                    user.value?.role === 'admin' ? api.getOrganizations() : Promise.resolve([])
                ]);
                
                conversations.value = conversationsData;
                customers.value = customersData;
                teams.value = teamsData;
                organizations.value = organizationsData;
                
            } catch (error) {
                console.error('Error loading data:', error);
                showToast('Failed to load conversations', 'error');
            } finally {
                loading.value = false;
            }
        };
        
        const refreshConversations = () => {
            loadData();
        };
        
        const applyFilters = () => {
            // Filters are reactive, so this just triggers the computed property
        };
        
        const openConversation = (conversationId) => {
            router.push(`/conversations/${conversationId}`);
        };
        
        const createConversation = async () => {
            creatingConversation.value = true;
            try {
                // Set default organization if user is not admin
                if (user.value?.role !== 'admin' && organizations.value.length > 0) {
                    newConversation.organization_id = organizations.value[0].id;
                }
                
                await api.createConversation(newConversation);
                showToast('Conversation created successfully', 'success');
                showCreateModal.value = false;
                
                // Reset form
                newConversation.customer_id = '';
                newConversation.organization_id = '';
                
                // Refresh data
                loadData();
                
            } catch (error) {
                console.error('Error creating conversation:', error);
                showToast('Failed to create conversation', 'error');
            } finally {
                creatingConversation.value = false;
            }
        };
        
        const assignTeam = (conversation) => {
            assignData.conversation_id = conversation.id;
            assignData.team_id = '';
            showAssignModal.value = true;
        };
        
        const assignTeamToConversation = async () => {
            assigningTeam.value = true;
            try {
                await api.assignTeamToConversation(assignData.conversation_id, assignData.team_id);
                showToast('Team assigned successfully', 'success');
                showAssignModal.value = false;
                
                // Refresh data
                loadData();
                
            } catch (error) {
                console.error('Error assigning team:', error);
                showToast('Failed to assign team', 'error');
            } finally {
                assigningTeam.value = false;
            }
        };
        
        const formatTime = (timestamp) => {
            const date = new Date(timestamp);
            const now = new Date();
            const diff = now - date;
            
            if (diff < 60000) return 'Just now';
            if (diff < 3600000) return `${Math.floor(diff / 60000)}m ago`;
            if (diff < 86400000) return `${Math.floor(diff / 3600000)}h ago`;
            return `${Math.floor(diff / 86400000)}d ago`;
        };
        
        const getStatusColor = (status) => {
            const colors = {
                'new': 'info',
                'active': 'success',
                'pending': 'warning',
                'resolved': 'secondary',
                'closed': 'secondary'
            };
            return colors[status] || 'secondary';
        };
        
        onMounted(() => {
            loadData();
        });
        
        return {
            loading,
            creatingConversation,
            assigningTeam,
            showCreateModal,
            showAssignModal,
            user,
            conversations,
            customers,
            teams,
            organizations,
            filters,
            newConversation,
            assignData,
            filteredConversations,
            refreshConversations,
            applyFilters,
            openConversation,
            createConversation,
            assignTeam,
            assignTeamToConversation,
            formatTime,
            getStatusColor
        };
    }
};
