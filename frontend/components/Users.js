const UsersComponent = {
    template: `
        <div class="users">
            <div class="page-header">
                <div class="header-content">
                    <h1><i class="fas fa-user-cog"></i> Users</h1>
                    <p>Manage system users and their permissions</p>
                </div>
                <div class="header-actions">
                    <button @click="refreshUsers" class="btn btn-secondary">
                        <i class="fas fa-sync-alt" :class="{ 'fa-spin': loading }"></i>
                        Refresh
                    </button>
                    <button @click="showCreateModal = true" class="btn btn-primary">
                        <i class="fas fa-user-plus"></i>
                        Add User
                    </button>
                </div>
            </div>

            <!-- Filters -->
            <div class="filters-card card">
                <div class="filters-grid">
                    <div class="filter-group">
                        <label class="form-label">Role</label>
                        <select v-model="filters.role" @change="applyFilters" class="form-control form-select">
                            <option value="">All Roles</option>
                            <option value="admin">Admin</option>
                            <option value="agent">Agent</option>
                        </select>
                    </div>
                    
                    <div class="filter-group">
                        <label class="form-label">Team</label>
                        <select v-model="filters.team" @change="applyFilters" class="form-control form-select">
                            <option value="">All Teams</option>
                            <option value="unassigned">Unassigned</option>
                            <option v-for="team in teams" :key="team.id" :value="team.id">
                                {{ team.name }}
                            </option>
                        </select>
                    </div>
                    
                    <div class="filter-group">
                        <label class="form-label">Search</label>
                        <input 
                            v-model="filters.search" 
                            @input="applyFilters"
                            type="text" 
                            class="form-control" 
                            placeholder="Search users..."
                        >
                    </div>
                </div>
            </div>

            <!-- Users Table -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        Users ({{ filteredUsers.length }})
                    </h3>
                </div>

                <div v-if="loading" class="loading-state">
                    <i class="fas fa-spinner fa-spin"></i>
                    <p>Loading users...</p>
                </div>

                <div v-else-if="filteredUsers.length === 0" class="empty-state">
                    <i class="fas fa-user-slash"></i>
                    <p>No users found</p>
                    <button @click="showCreateModal = true" class="btn btn-primary">
                        <i class="fas fa-user-plus"></i>
                        Add First User
                    </button>
                </div>

                <div v-else class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>User</th>
                                <th>Role</th>
                                <th>Team</th>
                                <th>Status</th>
                                <th>Last Login</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr v-for="user in filteredUsers" :key="user.id">
                                <td>
                                    <div class="user-info">
                                        <div class="user-avatar">
                                            <i class="fas fa-user"></i>
                                        </div>
                                        <div>
                                            <strong>{{ user.full_name }}</strong>
                                            <br>
                                            <small class="text-muted">{{ user.email }}</small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <span :class="['badge', \`badge-\${getRoleColor(user.role)}\`]">
                                        {{ user.role }}
                                    </span>
                                </td>
                                <td>
                                    <span v-if="user.team" class="team-badge">
                                        <i class="fas fa-users"></i>
                                        {{ user.team.name }}
                                    </span>
                                    <span v-else class="text-muted">
                                        <i class="fas fa-minus"></i>
                                        Unassigned
                                    </span>
                                </td>
                                <td>
                                    <span :class="['badge', user.is_active ? 'badge-success' : 'badge-secondary']">
                                        {{ user.is_active ? 'Active' : 'Inactive' }}
                                    </span>
                                </td>
                                <td>
                                    <span v-if="user.last_login">
                                        {{ formatDate(user.last_login) }}
                                    </span>
                                    <span v-else class="text-muted">Never</span>
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <button 
                                            @click="editUser(user)" 
                                            class="btn btn-sm btn-secondary"
                                            title="Edit User"
                                        >
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button 
                                            @click="toggleUserStatus(user)" 
                                            :class="['btn', 'btn-sm', user.is_active ? 'btn-warning' : 'btn-success']"
                                            :title="user.is_active ? 'Deactivate User' : 'Activate User'"
                                        >
                                            <i :class="user.is_active ? 'fas fa-user-slash' : 'fas fa-user-check'"></i>
                                        </button>
                                        <button 
                                            @click="deleteUser(user)" 
                                            class="btn btn-sm btn-danger"
                                            title="Delete User"
                                            :disabled="user.id === currentUser?.id"
                                        >
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Create/Edit User Modal -->
            <div v-if="showCreateModal || showEditModal" class="modal-overlay" @click="closeModal">
                <div class="modal modal-lg" @click.stop>
                    <div class="modal-header">
                        <h3>
                            <i :class="showCreateModal ? 'fas fa-user-plus' : 'fas fa-user-edit'"></i>
                            {{ showCreateModal ? 'Add New User' : 'Edit User' }}
                        </h3>
                        <button @click="closeModal" class="modal-close">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    
                    <form @submit.prevent="saveUser" class="modal-body">
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">Full Name *</label>
                                <input 
                                    v-model="userForm.full_name" 
                                    type="text" 
                                    class="form-control" 
                                    placeholder="John Doe"
                                    required
                                >
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">Email Address *</label>
                                <input 
                                    v-model="userForm.email" 
                                    type="email" 
                                    class="form-control" 
                                    placeholder="<EMAIL>"
                                    required
                                    :disabled="showEditModal"
                                >
                            </div>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">Role *</label>
                                <select v-model="userForm.role" class="form-control form-select" required>
                                    <option value="">Select a role</option>
                                    <option value="admin">Admin</option>
                                    <option value="agent">Agent</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">Team</label>
                                <select v-model="userForm.team_id" class="form-control form-select">
                                    <option value="">No team assigned</option>
                                    <option v-for="team in teams" :key="team.id" :value="team.id">
                                        {{ team.name }}
                                    </option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="form-row" v-if="showCreateModal">
                            <div class="form-group">
                                <label class="form-label">Password *</label>
                                <input 
                                    v-model="userForm.password" 
                                    type="password" 
                                    class="form-control" 
                                    placeholder="Enter password"
                                    :required="showCreateModal"
                                >
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">Confirm Password *</label>
                                <input 
                                    v-model="userForm.confirm_password" 
                                    type="password" 
                                    class="form-control" 
                                    placeholder="Confirm password"
                                    :required="showCreateModal"
                                >
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-check-label">
                                <input 
                                    v-model="userForm.is_active" 
                                    type="checkbox" 
                                    class="form-check-input"
                                >
                                Active User
                            </label>
                        </div>
                        
                        <div v-if="showCreateModal && userForm.password !== userForm.confirm_password" class="error-message">
                            <i class="fas fa-exclamation-circle"></i>
                            Passwords do not match
                        </div>
                        
                        <div class="modal-footer">
                            <button type="button" @click="closeModal" class="btn btn-secondary">
                                Cancel
                            </button>
                            <button 
                                type="submit" 
                                class="btn btn-primary" 
                                :disabled="savingUser || (showCreateModal && userForm.password !== userForm.confirm_password)"
                            >
                                <i v-if="savingUser" class="fas fa-spinner fa-spin"></i>
                                <i v-else :class="showCreateModal ? 'fas fa-user-plus' : 'fas fa-save'"></i>
                                {{ savingUser ? 'Saving...' : (showCreateModal ? 'Add User' : 'Save Changes') }}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    `,
    
    setup() {
        const { ref, reactive, computed, onMounted } = Vue;
        
        const loading = ref(false);
        const savingUser = ref(false);
        const showCreateModal = ref(false);
        const showEditModal = ref(false);
        
        const currentUser = computed(() => globalState.user);
        
        const users = ref([]);
        const teams = ref([]);
        
        const filters = reactive({
            role: '',
            team: '',
            search: ''
        });
        
        const userForm = reactive({
            id: null,
            full_name: '',
            email: '',
            role: '',
            team_id: '',
            password: '',
            confirm_password: '',
            is_active: true
        });
        
        const filteredUsers = computed(() => {
            let filtered = users.value;
            
            if (filters.role) {
                filtered = filtered.filter(u => u.role === filters.role);
            }
            
            if (filters.team) {
                if (filters.team === 'unassigned') {
                    filtered = filtered.filter(u => !u.team_id);
                } else {
                    filtered = filtered.filter(u => u.team_id == filters.team);
                }
            }
            
            if (filters.search) {
                const search = filters.search.toLowerCase();
                filtered = filtered.filter(u => 
                    u.full_name?.toLowerCase().includes(search) ||
                    u.email?.toLowerCase().includes(search)
                );
            }
            
            return filtered.sort((a, b) => a.full_name.localeCompare(b.full_name));
        });
        
        const loadData = async () => {
            loading.value = true;
            try {
                const [usersData, teamsData] = await Promise.all([
                    api.getUsers(),
                    api.getTeams()
                ]);
                
                users.value = usersData;
                teams.value = teamsData;
                
            } catch (error) {
                console.error('Error loading users data:', error);
                showToast('Failed to load users', 'error');
            } finally {
                loading.value = false;
            }
        };
        
        const refreshUsers = () => {
            loadData();
        };
        
        const applyFilters = () => {
            // Filters are reactive, so this just triggers the computed property
        };
        
        const getRoleColor = (role) => {
            const colors = {
                'admin': 'danger',
                'agent': 'primary'
            };
            return colors[role] || 'secondary';
        };
        
        const formatDate = (timestamp) => {
            return new Date(timestamp).toLocaleDateString();
        };
        
        const resetForm = () => {
            userForm.id = null;
            userForm.full_name = '';
            userForm.email = '';
            userForm.role = '';
            userForm.team_id = '';
            userForm.password = '';
            userForm.confirm_password = '';
            userForm.is_active = true;
        };
        
        const closeModal = () => {
            showCreateModal.value = false;
            showEditModal.value = false;
            resetForm();
        };
        
        const editUser = (user) => {
            userForm.id = user.id;
            userForm.full_name = user.full_name;
            userForm.email = user.email;
            userForm.role = user.role;
            userForm.team_id = user.team_id || '';
            userForm.is_active = user.is_active;
            showEditModal.value = true;
        };
        
        const saveUser = async () => {
            savingUser.value = true;
            try {
                const userData = {
                    full_name: userForm.full_name,
                    email: userForm.email,
                    role: userForm.role,
                    team_id: userForm.team_id || null,
                    is_active: userForm.is_active
                };
                
                if (showCreateModal.value) {
                    userData.password = userForm.password;
                    await api.createUser(userData);
                    showToast('User created successfully', 'success');
                } else {
                    await api.updateUser(userForm.id, userData);
                    showToast('User updated successfully', 'success');
                }
                
                closeModal();
                loadData();
                
            } catch (error) {
                console.error('Error saving user:', error);
                if (error.response?.status === 400 && error.response?.data?.detail?.includes('already exists')) {
                    showToast('Email already exists', 'error');
                } else {
                    showToast('Failed to save user', 'error');
                }
            } finally {
                savingUser.value = false;
            }
        };
        
        const toggleUserStatus = async (user) => {
            try {
                const updatedUser = { ...user, is_active: !user.is_active };
                await api.updateUser(user.id, updatedUser);
                showToast(`User ${updatedUser.is_active ? 'activated' : 'deactivated'} successfully`, 'success');
                loadData();
            } catch (error) {
                console.error('Error updating user status:', error);
                showToast('Failed to update user status', 'error');
            }
        };
        
        const deleteUser = async (user) => {
            if (user.id === currentUser.value?.id) {
                showToast('You cannot delete your own account', 'error');
                return;
            }
            
            if (!confirm(`Are you sure you want to delete user "${user.full_name}"?`)) {
                return;
            }
            
            try {
                await api.deleteUser(user.id);
                showToast('User deleted successfully', 'success');
                loadData();
            } catch (error) {
                console.error('Error deleting user:', error);
                showToast('Failed to delete user', 'error');
            }
        };
        
        onMounted(() => {
            loadData();
        });
        
        return {
            loading,
            savingUser,
            showCreateModal,
            showEditModal,
            currentUser,
            users,
            teams,
            filters,
            userForm,
            filteredUsers,
            refreshUsers,
            applyFilters,
            getRoleColor,
            formatDate,
            closeModal,
            editUser,
            saveUser,
            toggleUserStatus,
            deleteUser
        };
    }
};
