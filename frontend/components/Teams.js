const TeamsComponent = {
    template: `
        <div class="teams">
            <div class="page-header">
                <div class="header-content">
                    <h1><i class="fas fa-user-friends"></i> Teams</h1>
                    <p>Manage support teams and agent assignments</p>
                </div>
                <div class="header-actions">
                    <button @click="refreshTeams" class="btn btn-secondary">
                        <i class="fas fa-sync-alt" :class="{ 'fa-spin': loading }"></i>
                        Refresh
                    </button>
                    <button @click="showCreateModal = true" class="btn btn-primary">
                        <i class="fas fa-plus"></i>
                        Create Team
                    </button>
                </div>
            </div>

            <!-- Teams Grid -->
            <div v-if="loading" class="loading-state">
                <i class="fas fa-spinner fa-spin"></i>
                <p>Loading teams...</p>
            </div>

            <div v-else-if="teams.length === 0" class="empty-state">
                <i class="fas fa-users-slash"></i>
                <p>No teams found</p>
                <button @click="showCreateModal = true" class="btn btn-primary">
                    <i class="fas fa-plus"></i>
                    Create First Team
                </button>
            </div>

            <div v-else class="teams-grid">
                <div v-for="team in teams" :key="team.id" class="team-card">
                    <div class="team-header">
                        <div class="team-info">
                            <h3>{{ team.name }}</h3>
                            <p>{{ team.description }}</p>
                        </div>
                        <div class="team-actions">
                            <button @click="editTeam(team)" class="btn btn-sm btn-secondary">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button @click="deleteTeam(team)" class="btn btn-sm btn-danger">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>

                    <div class="team-stats">
                        <div class="stat-item">
                            <i class="fas fa-users"></i>
                            <span>{{ getTeamMemberCount(team.id) }} Members</span>
                        </div>
                        <div class="stat-item">
                            <i class="fas fa-comments"></i>
                            <span>{{ getTeamConversationCount(team.id) }} Conversations</span>
                        </div>
                        <div class="stat-item">
                            <i class="fas fa-building"></i>
                            <span>{{ team.organization?.name || 'No Organization' }}</span>
                        </div>
                    </div>

                    <div class="team-members">
                        <h4>Team Members</h4>
                        <div v-if="getTeamMembers(team.id).length === 0" class="no-members">
                            <i class="fas fa-user-slash"></i>
                            <span>No members assigned</span>
                        </div>
                        <div v-else class="members-list">
                            <div v-for="member in getTeamMembers(team.id)" :key="member.id" class="member-item">
                                <div class="member-avatar">
                                    <i class="fas fa-user"></i>
                                </div>
                                <div class="member-info">
                                    <strong>{{ member.full_name }}</strong>
                                    <span class="member-role">{{ member.role }}</span>
                                </div>
                                <button 
                                    @click="removeMemberFromTeam(team.id, member.id)"
                                    class="btn btn-sm btn-danger"
                                    title="Remove from team"
                                >
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                        <button @click="showAddMemberModal(team)" class="btn btn-sm btn-primary add-member-btn">
                            <i class="fas fa-user-plus"></i>
                            Add Member
                        </button>
                    </div>
                </div>
            </div>

            <!-- Create/Edit Team Modal -->
            <div v-if="showCreateModal || showEditModal" class="modal-overlay" @click="closeModal">
                <div class="modal" @click.stop>
                    <div class="modal-header">
                        <h3>
                            <i :class="showCreateModal ? 'fas fa-plus' : 'fas fa-edit'"></i>
                            {{ showCreateModal ? 'Create New Team' : 'Edit Team' }}
                        </h3>
                        <button @click="closeModal" class="modal-close">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    
                    <form @submit.prevent="saveTeam" class="modal-body">
                        <div class="form-group">
                            <label class="form-label">Team Name *</label>
                            <input 
                                v-model="teamForm.name" 
                                type="text" 
                                class="form-control" 
                                placeholder="e.g., Customer Support"
                                required
                            >
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">Description</label>
                            <textarea 
                                v-model="teamForm.description" 
                                class="form-control" 
                                rows="3"
                                placeholder="Brief description of the team's role"
                            ></textarea>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">Organization *</label>
                            <select v-model="teamForm.organization_id" class="form-control form-select" required>
                                <option value="">Select an organization</option>
                                <option v-for="org in organizations" :key="org.id" :value="org.id">
                                    {{ org.name }}
                                </option>
                            </select>
                        </div>
                        
                        <div class="modal-footer">
                            <button type="button" @click="closeModal" class="btn btn-secondary">
                                Cancel
                            </button>
                            <button type="submit" class="btn btn-primary" :disabled="savingTeam">
                                <i v-if="savingTeam" class="fas fa-spinner fa-spin"></i>
                                <i v-else :class="showCreateModal ? 'fas fa-plus' : 'fas fa-save'"></i>
                                {{ savingTeam ? 'Saving...' : (showCreateModal ? 'Create Team' : 'Save Changes') }}
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Add Member Modal -->
            <div v-if="showMemberModal" class="modal-overlay" @click="showMemberModal = false">
                <div class="modal" @click.stop>
                    <div class="modal-header">
                        <h3><i class="fas fa-user-plus"></i> Add Team Member</h3>
                        <button @click="showMemberModal = false" class="modal-close">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    
                    <form @submit.prevent="addMemberToTeam" class="modal-body">
                        <div class="form-group">
                            <label class="form-label">Select User</label>
                            <select v-model="memberForm.user_id" class="form-control form-select" required>
                                <option value="">Choose a user</option>
                                <option 
                                    v-for="user in availableUsers" 
                                    :key="user.id" 
                                    :value="user.id"
                                >
                                    {{ user.full_name }} ({{ user.email }}) - {{ user.role }}
                                </option>
                            </select>
                        </div>
                        
                        <div class="modal-footer">
                            <button type="button" @click="showMemberModal = false" class="btn btn-secondary">
                                Cancel
                            </button>
                            <button type="submit" class="btn btn-primary" :disabled="addingMember">
                                <i v-if="addingMember" class="fas fa-spinner fa-spin"></i>
                                <i v-else class="fas fa-user-plus"></i>
                                {{ addingMember ? 'Adding...' : 'Add Member' }}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    `,
    
    setup() {
        const { ref, reactive, computed, onMounted } = Vue;
        
        const loading = ref(false);
        const savingTeam = ref(false);
        const addingMember = ref(false);
        const showCreateModal = ref(false);
        const showEditModal = ref(false);
        const showMemberModal = ref(false);
        
        const teams = ref([]);
        const organizations = ref([]);
        const users = ref([]);
        const conversations = ref([]);
        
        const teamForm = reactive({
            id: null,
            name: '',
            description: '',
            organization_id: ''
        });
        
        const memberForm = reactive({
            team_id: null,
            user_id: ''
        });
        
        const availableUsers = computed(() => {
            if (!memberForm.team_id) return [];
            
            const teamMembers = getTeamMembers(memberForm.team_id);
            const memberIds = teamMembers.map(m => m.id);
            
            return users.value.filter(user => !memberIds.includes(user.id));
        });
        
        const loadData = async () => {
            loading.value = true;
            try {
                const [teamsData, organizationsData, usersData, conversationsData] = await Promise.all([
                    api.getTeams(),
                    api.getOrganizations(),
                    api.getUsers(),
                    api.getConversations()
                ]);
                
                teams.value = teamsData;
                organizations.value = organizationsData;
                users.value = usersData;
                conversations.value = conversationsData;
                
            } catch (error) {
                console.error('Error loading teams data:', error);
                showToast('Failed to load teams', 'error');
            } finally {
                loading.value = false;
            }
        };
        
        const refreshTeams = () => {
            loadData();
        };
        
        const getTeamMemberCount = (teamId) => {
            return users.value.filter(user => user.team_id === teamId).length;
        };
        
        const getTeamConversationCount = (teamId) => {
            return conversations.value.filter(conv => conv.team_id === teamId).length;
        };
        
        const getTeamMembers = (teamId) => {
            return users.value.filter(user => user.team_id === teamId);
        };
        
        const resetForm = () => {
            teamForm.id = null;
            teamForm.name = '';
            teamForm.description = '';
            teamForm.organization_id = '';
        };
        
        const closeModal = () => {
            showCreateModal.value = false;
            showEditModal.value = false;
            resetForm();
        };
        
        const editTeam = (team) => {
            teamForm.id = team.id;
            teamForm.name = team.name;
            teamForm.description = team.description || '';
            teamForm.organization_id = team.organization_id;
            showEditModal.value = true;
        };
        
        const saveTeam = async () => {
            savingTeam.value = true;
            try {
                const teamData = {
                    name: teamForm.name,
                    description: teamForm.description || null,
                    organization_id: teamForm.organization_id
                };
                
                if (showCreateModal.value) {
                    await api.createTeam(teamData);
                    showToast('Team created successfully', 'success');
                } else {
                    await api.updateTeam(teamForm.id, teamData);
                    showToast('Team updated successfully', 'success');
                }
                
                closeModal();
                loadData();
                
            } catch (error) {
                console.error('Error saving team:', error);
                showToast('Failed to save team', 'error');
            } finally {
                savingTeam.value = false;
            }
        };
        
        const deleteTeam = async (team) => {
            if (!confirm(`Are you sure you want to delete team "${team.name}"?`)) {
                return;
            }
            
            try {
                await api.deleteTeam(team.id);
                showToast('Team deleted successfully', 'success');
                loadData();
            } catch (error) {
                console.error('Error deleting team:', error);
                showToast('Failed to delete team', 'error');
            }
        };
        
        const showAddMemberModal = (team) => {
            memberForm.team_id = team.id;
            memberForm.user_id = '';
            showMemberModal.value = true;
        };
        
        const addMemberToTeam = async () => {
            addingMember.value = true;
            try {
                // Update user's team_id
                const user = users.value.find(u => u.id === memberForm.user_id);
                if (user) {
                    await api.updateUser(user.id, { ...user, team_id: memberForm.team_id });
                    showToast('Member added to team successfully', 'success');
                    showMemberModal.value = false;
                    loadData();
                }
            } catch (error) {
                console.error('Error adding member to team:', error);
                showToast('Failed to add member to team', 'error');
            } finally {
                addingMember.value = false;
            }
        };
        
        const removeMemberFromTeam = async (teamId, userId) => {
            if (!confirm('Are you sure you want to remove this member from the team?')) {
                return;
            }
            
            try {
                const user = users.value.find(u => u.id === userId);
                if (user) {
                    await api.updateUser(user.id, { ...user, team_id: null });
                    showToast('Member removed from team successfully', 'success');
                    loadData();
                }
            } catch (error) {
                console.error('Error removing member from team:', error);
                showToast('Failed to remove member from team', 'error');
            }
        };
        
        onMounted(() => {
            loadData();
        });
        
        return {
            loading,
            savingTeam,
            addingMember,
            showCreateModal,
            showEditModal,
            showMemberModal,
            teams,
            organizations,
            users,
            teamForm,
            memberForm,
            availableUsers,
            refreshTeams,
            getTeamMemberCount,
            getTeamConversationCount,
            getTeamMembers,
            closeModal,
            editTeam,
            saveTeam,
            deleteTeam,
            showAddMemberModal,
            addMemberToTeam,
            removeMemberFromTeam
        };
    }
};
