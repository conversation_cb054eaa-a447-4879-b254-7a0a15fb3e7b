const CustomersComponent = {
    template: `
        <div class="customers">
            <div class="page-header">
                <div class="header-content">
                    <h1><i class="fas fa-users"></i> Customers</h1>
                    <p>Manage customer information and profiles</p>
                </div>
                <div class="header-actions">
                    <button @click="refreshCustomers" class="btn btn-secondary">
                        <i class="fas fa-sync-alt" :class="{ 'fa-spin': loading }"></i>
                        Refresh
                    </button>
                    <button @click="showCreateModal = true" class="btn btn-primary">
                        <i class="fas fa-user-plus"></i>
                        Add Customer
                    </button>
                </div>
            </div>

            <!-- Search and Filters -->
            <div class="filters-card card">
                <div class="filters-grid">
                    <div class="filter-group">
                        <label class="form-label">Search</label>
                        <input 
                            v-model="searchQuery" 
                            @input="applyFilters"
                            type="text" 
                            class="form-control" 
                            placeholder="Search by name, email, or customer ID..."
                        >
                    </div>
                    
                    <div class="filter-group">
                        <label class="form-label">Sort By</label>
                        <select v-model="sortBy" @change="applyFilters" class="form-control form-select">
                            <option value="created_at">Date Created</option>
                            <option value="name">Name</option>
                            <option value="email">Email</option>
                            <option value="customer_id">Customer ID</option>
                        </select>
                    </div>
                    
                    <div class="filter-group">
                        <label class="form-label">Order</label>
                        <select v-model="sortOrder" @change="applyFilters" class="form-control form-select">
                            <option value="desc">Newest First</option>
                            <option value="asc">Oldest First</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Customers Table -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        Customers ({{ filteredCustomers.length }})
                    </h3>
                </div>

                <div v-if="loading" class="loading-state">
                    <i class="fas fa-spinner fa-spin"></i>
                    <p>Loading customers...</p>
                </div>

                <div v-else-if="filteredCustomers.length === 0" class="empty-state">
                    <i class="fas fa-user-slash"></i>
                    <p>No customers found</p>
                    <button @click="showCreateModal = true" class="btn btn-primary">
                        <i class="fas fa-user-plus"></i>
                        Add First Customer
                    </button>
                </div>

                <div v-else class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Customer</th>
                                <th>Contact Info</th>
                                <th>Location</th>
                                <th>Created</th>
                                <th>Conversations</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr v-for="customer in paginatedCustomers" :key="customer.id">
                                <td>
                                    <div class="customer-info">
                                        <div class="customer-avatar">
                                            <i class="fas fa-user"></i>
                                        </div>
                                        <div>
                                            <strong>{{ customer.name }}</strong>
                                            <br>
                                            <small class="text-muted">ID: {{ customer.customer_id }}</small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div>
                                        <i class="fas fa-envelope"></i>
                                        {{ customer.email || 'No email' }}
                                    </div>
                                    <div v-if="customer.phone">
                                        <i class="fas fa-phone"></i>
                                        {{ customer.phone }}
                                    </div>
                                </td>
                                <td>
                                    <div v-if="customer.location">
                                        <i class="fas fa-map-marker-alt"></i>
                                        {{ customer.location }}
                                    </div>
                                    <div v-if="customer.ip_address" class="text-muted">
                                        <small>IP: {{ customer.ip_address }}</small>
                                    </div>
                                </td>
                                <td>
                                    {{ formatDate(customer.created_at) }}
                                </td>
                                <td>
                                    <span class="badge badge-info">
                                        {{ getCustomerConversationCount(customer.id) }}
                                    </span>
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <button 
                                            @click="editCustomer(customer)" 
                                            class="btn btn-sm btn-secondary"
                                            title="Edit Customer"
                                        >
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button 
                                            @click="viewCustomerConversations(customer)" 
                                            class="btn btn-sm btn-primary"
                                            title="View Conversations"
                                        >
                                            <i class="fas fa-comments"></i>
                                        </button>
                                        <button 
                                            @click="deleteCustomer(customer)" 
                                            class="btn btn-sm btn-danger"
                                            title="Delete Customer"
                                            v-if="user?.role === 'admin'"
                                        >
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div v-if="totalPages > 1" class="pagination-container">
                    <div class="pagination-info">
                        Showing {{ (currentPage - 1) * pageSize + 1 }} to 
                        {{ Math.min(currentPage * pageSize, filteredCustomers.length) }} 
                        of {{ filteredCustomers.length }} customers
                    </div>
                    <div class="pagination">
                        <button 
                            @click="currentPage = 1" 
                            :disabled="currentPage === 1"
                            class="btn btn-sm btn-secondary"
                        >
                            <i class="fas fa-angle-double-left"></i>
                        </button>
                        <button 
                            @click="currentPage--" 
                            :disabled="currentPage === 1"
                            class="btn btn-sm btn-secondary"
                        >
                            <i class="fas fa-angle-left"></i>
                        </button>
                        <span class="pagination-current">
                            Page {{ currentPage }} of {{ totalPages }}
                        </span>
                        <button 
                            @click="currentPage++" 
                            :disabled="currentPage === totalPages"
                            class="btn btn-sm btn-secondary"
                        >
                            <i class="fas fa-angle-right"></i>
                        </button>
                        <button 
                            @click="currentPage = totalPages" 
                            :disabled="currentPage === totalPages"
                            class="btn btn-sm btn-secondary"
                        >
                            <i class="fas fa-angle-double-right"></i>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Create/Edit Customer Modal -->
            <div v-if="showCreateModal || showEditModal" class="modal-overlay" @click="closeModal">
                <div class="modal modal-lg" @click.stop>
                    <div class="modal-header">
                        <h3>
                            <i :class="showCreateModal ? 'fas fa-user-plus' : 'fas fa-user-edit'"></i>
                            {{ showCreateModal ? 'Add New Customer' : 'Edit Customer' }}
                        </h3>
                        <button @click="closeModal" class="modal-close">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    
                    <form @submit.prevent="saveCustomer" class="modal-body">
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">Customer ID *</label>
                                <input 
                                    v-model="customerForm.customer_id" 
                                    type="text" 
                                    class="form-control" 
                                    placeholder="e.g., CUST-001"
                                    required
                                    :disabled="showEditModal"
                                >
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">Full Name *</label>
                                <input 
                                    v-model="customerForm.name" 
                                    type="text" 
                                    class="form-control" 
                                    placeholder="Customer full name"
                                    required
                                >
                            </div>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">Email Address</label>
                                <input 
                                    v-model="customerForm.email" 
                                    type="email" 
                                    class="form-control" 
                                    placeholder="<EMAIL>"
                                >
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">Phone Number</label>
                                <input 
                                    v-model="customerForm.phone" 
                                    type="tel" 
                                    class="form-control" 
                                    placeholder="+1234567890"
                                >
                            </div>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">Location</label>
                                <input 
                                    v-model="customerForm.location" 
                                    type="text" 
                                    class="form-control" 
                                    placeholder="City, Country"
                                >
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">IP Address</label>
                                <input 
                                    v-model="customerForm.ip_address" 
                                    type="text" 
                                    class="form-control" 
                                    placeholder="***********"
                                >
                            </div>
                        </div>
                        
                        <div class="modal-footer">
                            <button type="button" @click="closeModal" class="btn btn-secondary">
                                Cancel
                            </button>
                            <button type="submit" class="btn btn-primary" :disabled="savingCustomer">
                                <i v-if="savingCustomer" class="fas fa-spinner fa-spin"></i>
                                <i v-else :class="showCreateModal ? 'fas fa-plus' : 'fas fa-save'"></i>
                                {{ savingCustomer ? 'Saving...' : (showCreateModal ? 'Add Customer' : 'Save Changes') }}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    `,
    
    setup() {
        const { ref, reactive, computed, onMounted } = Vue;
        const router = VueRouter.useRouter();
        
        const loading = ref(false);
        const savingCustomer = ref(false);
        const showCreateModal = ref(false);
        const showEditModal = ref(false);
        
        const user = computed(() => globalState.user);
        
        const customers = ref([]);
        const conversations = ref([]);
        
        const searchQuery = ref('');
        const sortBy = ref('created_at');
        const sortOrder = ref('desc');
        const currentPage = ref(1);
        const pageSize = ref(10);
        
        const customerForm = reactive({
            id: null,
            customer_id: '',
            name: '',
            email: '',
            phone: '',
            location: '',
            ip_address: ''
        });
        
        const filteredCustomers = computed(() => {
            let filtered = customers.value;
            
            if (searchQuery.value) {
                const search = searchQuery.value.toLowerCase();
                filtered = filtered.filter(customer => 
                    customer.name?.toLowerCase().includes(search) ||
                    customer.email?.toLowerCase().includes(search) ||
                    customer.customer_id?.toLowerCase().includes(search)
                );
            }
            
            // Sort
            filtered.sort((a, b) => {
                let aVal = a[sortBy.value];
                let bVal = b[sortBy.value];
                
                if (sortBy.value === 'created_at') {
                    aVal = new Date(aVal);
                    bVal = new Date(bVal);
                }
                
                if (sortOrder.value === 'asc') {
                    return aVal > bVal ? 1 : -1;
                } else {
                    return aVal < bVal ? 1 : -1;
                }
            });
            
            return filtered;
        });
        
        const totalPages = computed(() => {
            return Math.ceil(filteredCustomers.value.length / pageSize.value);
        });
        
        const paginatedCustomers = computed(() => {
            const start = (currentPage.value - 1) * pageSize.value;
            const end = start + pageSize.value;
            return filteredCustomers.value.slice(start, end);
        });
        
        const loadData = async () => {
            loading.value = true;
            try {
                const [customersData, conversationsData] = await Promise.all([
                    api.getCustomers(),
                    api.getConversations()
                ]);
                
                customers.value = customersData;
                conversations.value = conversationsData;
                
            } catch (error) {
                console.error('Error loading customers:', error);
                showToast('Failed to load customers', 'error');
            } finally {
                loading.value = false;
            }
        };
        
        const refreshCustomers = () => {
            loadData();
        };
        
        const applyFilters = () => {
            currentPage.value = 1; // Reset to first page when filtering
        };
        
        const getCustomerConversationCount = (customerId) => {
            return conversations.value.filter(c => c.customer_id === customerId).length;
        };
        
        const formatDate = (timestamp) => {
            return new Date(timestamp).toLocaleDateString();
        };
        
        const resetForm = () => {
            customerForm.id = null;
            customerForm.customer_id = '';
            customerForm.name = '';
            customerForm.email = '';
            customerForm.phone = '';
            customerForm.location = '';
            customerForm.ip_address = '';
        };
        
        const closeModal = () => {
            showCreateModal.value = false;
            showEditModal.value = false;
            resetForm();
        };
        
        const editCustomer = (customer) => {
            customerForm.id = customer.id;
            customerForm.customer_id = customer.customer_id;
            customerForm.name = customer.name;
            customerForm.email = customer.email || '';
            customerForm.phone = customer.phone || '';
            customerForm.location = customer.location || '';
            customerForm.ip_address = customer.ip_address || '';
            showEditModal.value = true;
        };
        
        const saveCustomer = async () => {
            savingCustomer.value = true;
            try {
                const customerData = {
                    customer_id: customerForm.customer_id,
                    name: customerForm.name,
                    email: customerForm.email || null,
                    phone: customerForm.phone || null,
                    location: customerForm.location || null,
                    ip_address: customerForm.ip_address || null
                };
                
                if (showCreateModal.value) {
                    await api.createCustomer(customerData);
                    showToast('Customer created successfully', 'success');
                } else {
                    await api.updateCustomer(customerForm.id, customerData);
                    showToast('Customer updated successfully', 'success');
                }
                
                closeModal();
                loadData();
                
            } catch (error) {
                console.error('Error saving customer:', error);
                if (error.response?.status === 400 && error.response?.data?.detail?.includes('already exists')) {
                    showToast('Customer ID already exists', 'error');
                } else {
                    showToast('Failed to save customer', 'error');
                }
            } finally {
                savingCustomer.value = false;
            }
        };
        
        const deleteCustomer = async (customer) => {
            if (!confirm(`Are you sure you want to delete customer "${customer.name}"?`)) {
                return;
            }
            
            try {
                await api.deleteCustomer(customer.id);
                showToast('Customer deleted successfully', 'success');
                loadData();
            } catch (error) {
                console.error('Error deleting customer:', error);
                showToast('Failed to delete customer', 'error');
            }
        };
        
        const viewCustomerConversations = (customer) => {
            router.push(`/conversations?customer=${customer.id}`);
        };
        
        onMounted(() => {
            loadData();
        });
        
        return {
            loading,
            savingCustomer,
            showCreateModal,
            showEditModal,
            user,
            customers,
            searchQuery,
            sortBy,
            sortOrder,
            currentPage,
            pageSize,
            customerForm,
            filteredCustomers,
            totalPages,
            paginatedCustomers,
            refreshCustomers,
            applyFilters,
            getCustomerConversationCount,
            formatDate,
            closeModal,
            editCustomer,
            saveCustomer,
            deleteCustomer,
            viewCustomerConversations
        };
    }
};
