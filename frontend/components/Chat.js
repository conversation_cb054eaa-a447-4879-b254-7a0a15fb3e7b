const ChatComponent = {
    template: `
        <div class="chat-container">
            <div class="chat-header">
                <div class="chat-info">
                    <button @click="$router.go(-1)" class="btn btn-sm btn-secondary">
                        <i class="fas fa-arrow-left"></i>
                        Back
                    </button>
                    <div class="conversation-details" v-if="conversation">
                        <h2>{{ conversation.customer?.name || 'Unknown Customer' }}</h2>
                        <p>{{ conversation.customer?.email || 'No email' }}</p>
                        <span :class="['badge', \`badge-\${getStatusColor(conversation.status)}\`]">
                            {{ conversation.status }}
                        </span>
                    </div>
                </div>
                <div class="chat-actions" v-if="conversation">
                    <select 
                        v-model="conversation.status" 
                        @change="updateConversationStatus"
                        class="form-control form-select"
                    >
                        <option value="new">New</option>
                        <option value="active">Active</option>
                        <option value="pending">Pending</option>
                        <option value="resolved">Resolved</option>
                        <option value="closed">Closed</option>
                    </select>
                </div>
            </div>

            <div class="chat-body">
                <div class="messages-container" ref="messagesContainer">
                    <div v-if="loadingMessages" class="loading-state">
                        <i class="fas fa-spinner fa-spin"></i>
                        <p>Loading messages...</p>
                    </div>

                    <div v-else-if="messages.length === 0" class="empty-messages">
                        <i class="fas fa-comments"></i>
                        <p>No messages yet</p>
                        <small>Start the conversation by sending a message</small>
                    </div>

                    <div v-else class="messages-list">
                        <div 
                            v-for="message in messages" 
                            :key="message.id"
                            :class="['message', \`message-\${message.sender_type}\`]"
                        >
                            <div class="message-avatar">
                                <i :class="message.sender_type === 'customer' ? 'fas fa-user' : 'fas fa-user-tie'"></i>
                            </div>
                            
                            <div class="message-content">
                                <div class="message-header">
                                    <span class="message-sender">
                                        {{ message.sender_type === 'customer' ? 
                                            (conversation?.customer?.name || 'Customer') : 
                                            (message.sender?.full_name || 'Agent') 
                                        }}
                                    </span>
                                    <span class="message-time">
                                        {{ formatMessageTime(message.created_at) }}
                                    </span>
                                </div>
                                
                                <div class="message-body">
                                    <div v-if="message.message_type === 'text'" class="message-text">
                                        {{ message.content }}
                                    </div>
                                    
                                    <div v-else-if="message.message_type === 'file'" class="message-file">
                                        <i class="fas fa-file"></i>
                                        <a :href="message.file_url" target="_blank">
                                            {{ message.content || 'File attachment' }}
                                        </a>
                                    </div>
                                    
                                    <div v-else-if="message.message_type === 'image'" class="message-image">
                                        <img :src="message.file_url" :alt="message.content" @click="showImageModal(message.file_url)">
                                    </div>
                                </div>
                                
                                <div class="message-actions" v-if="message.sender_type === 'agent'">
                                    <button 
                                        @click="deleteMessage(message)"
                                        class="btn btn-sm btn-danger"
                                        title="Delete message"
                                    >
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="message-input">
                    <form @submit.prevent="sendMessage" class="input-form">
                        <div class="input-group">
                            <input 
                                v-model="newMessage"
                                type="text" 
                                class="form-control" 
                                placeholder="Type your message..."
                                :disabled="sendingMessage"
                            >
                            <input 
                                type="file" 
                                ref="fileInput"
                                @change="handleFileSelect"
                                style="display: none"
                                accept="image/*,.pdf,.doc,.docx,.txt"
                            >
                            <button 
                                type="button"
                                @click="$refs.fileInput.click()"
                                class="btn btn-secondary"
                                :disabled="sendingMessage"
                            >
                                <i class="fas fa-paperclip"></i>
                            </button>
                            <button 
                                type="submit" 
                                class="btn btn-primary"
                                :disabled="sendingMessage || (!newMessage.trim() && !selectedFile)"
                            >
                                <i v-if="sendingMessage" class="fas fa-spinner fa-spin"></i>
                                <i v-else class="fas fa-paper-plane"></i>
                                Send
                            </button>
                        </div>
                        
                        <div v-if="selectedFile" class="selected-file">
                            <i class="fas fa-file"></i>
                            <span>{{ selectedFile.name }}</span>
                            <button type="button" @click="clearSelectedFile" class="btn btn-sm btn-danger">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Image Modal -->
            <div v-if="showImageModalFlag" class="modal-overlay" @click="showImageModalFlag = false">
                <div class="image-modal" @click.stop>
                    <button @click="showImageModalFlag = false" class="modal-close">
                        <i class="fas fa-times"></i>
                    </button>
                    <img :src="modalImageUrl" alt="Full size image">
                </div>
            </div>
        </div>
    `,
    
    setup() {
        const { ref, reactive, onMounted, onUnmounted, nextTick } = Vue;
        const route = VueRouter.useRoute();
        const router = VueRouter.useRouter();
        
        const conversation = ref(null);
        const messages = ref([]);
        const loadingMessages = ref(false);
        const sendingMessage = ref(false);
        const newMessage = ref('');
        const selectedFile = ref(null);
        const showImageModalFlag = ref(false);
        const modalImageUrl = ref('');
        const messagesContainer = ref(null);
        
        let websocket = null;
        
        const conversationId = route.params.id;
        
        const loadConversation = async () => {
            try {
                const conversationData = await api.getConversation(conversationId);
                conversation.value = conversationData;
            } catch (error) {
                console.error('Error loading conversation:', error);
                showToast('Failed to load conversation', 'error');
                router.push('/conversations');
            }
        };
        
        const loadMessages = async () => {
            loadingMessages.value = true;
            try {
                const messagesData = await api.getConversationMessages(conversationId);
                messages.value = messagesData;
                await nextTick();
                scrollToBottom();
            } catch (error) {
                console.error('Error loading messages:', error);
                showToast('Failed to load messages', 'error');
            } finally {
                loadingMessages.value = false;
            }
        };
        
        const connectWebSocket = () => {
            const wsUrl = \`ws://0.0.0.0:8000/api/ws/chat/\${conversationId}\`;
            websocket = new WebSocket(wsUrl);
            
            websocket.onopen = () => {
                console.log('WebSocket connected');
            };
            
            websocket.onmessage = (event) => {
                const data = JSON.parse(event.data);
                if (data.type === 'message') {
                    messages.value.push(data.message);
                    nextTick(() => scrollToBottom());
                }
            };
            
            websocket.onclose = () => {
                console.log('WebSocket disconnected');
                // Attempt to reconnect after 3 seconds
                setTimeout(connectWebSocket, 3000);
            };
            
            websocket.onerror = (error) => {
                console.error('WebSocket error:', error);
            };
        };
        
        const sendMessage = async () => {
            if (!newMessage.value.trim() && !selectedFile.value) return;
            
            sendingMessage.value = true;
            try {
                if (selectedFile.value) {
                    // Upload file first
                    const formData = new FormData();
                    formData.append('file', selectedFile.value);
                    formData.append('conversation_id', conversationId);
                    
                    const uploadResponse = await fetch('http://0.0.0.0:8000/api/media/upload', {
                        method: 'POST',
                        body: formData,
                        credentials: 'include'
                    });
                    
                    if (!uploadResponse.ok) {
                        throw new Error('File upload failed');
                    }
                    
                    const uploadData = await uploadResponse.json();
                    
                    // Send file message via WebSocket
                    const fileMessage = {
                        content: newMessage.value || selectedFile.value.name,
                        message_type: selectedFile.value.type.startsWith('image/') ? 'image' : 'file',
                        file_url: uploadData.file_url
                    };
                    
                    websocket.send(JSON.stringify(fileMessage));
                    clearSelectedFile();
                } else {
                    // Send text message via WebSocket
                    const textMessage = {
                        content: newMessage.value,
                        message_type: 'text'
                    };
                    
                    websocket.send(JSON.stringify(textMessage));
                }
                
                newMessage.value = '';
                
            } catch (error) {
                console.error('Error sending message:', error);
                showToast('Failed to send message', 'error');
            } finally {
                sendingMessage.value = false;
            }
        };
        
        const handleFileSelect = (event) => {
            const file = event.target.files[0];
            if (file) {
                selectedFile.value = file;
            }
        };
        
        const clearSelectedFile = () => {
            selectedFile.value = null;
            if (messagesContainer.value) {
                messagesContainer.value.querySelector('input[type="file"]').value = '';
            }
        };
        
        const deleteMessage = async (message) => {
            if (!confirm('Are you sure you want to delete this message?')) {
                return;
            }
            
            try {
                await fetch(\`http://0.0.0.0:8000/api/messages/\${message.id}\`, {
                    method: 'DELETE',
                    credentials: 'include'
                });
                
                // Remove message from local array
                const index = messages.value.findIndex(m => m.id === message.id);
                if (index > -1) {
                    messages.value.splice(index, 1);
                }
                
                showToast('Message deleted successfully', 'success');
            } catch (error) {
                console.error('Error deleting message:', error);
                showToast('Failed to delete message', 'error');
            }
        };
        
        const updateConversationStatus = async () => {
            try {
                // This would need an API endpoint to update conversation status
                showToast('Conversation status updated', 'success');
            } catch (error) {
                console.error('Error updating conversation status:', error);
                showToast('Failed to update conversation status', 'error');
            }
        };
        
        const showImageModal = (imageUrl) => {
            modalImageUrl.value = imageUrl;
            showImageModalFlag.value = true;
        };
        
        const scrollToBottom = () => {
            if (messagesContainer.value) {
                const container = messagesContainer.value;
                container.scrollTop = container.scrollHeight;
            }
        };
        
        const formatMessageTime = (timestamp) => {
            const date = new Date(timestamp);
            const now = new Date();
            const diff = now - date;
            
            if (diff < 60000) return 'Just now';
            if (diff < 3600000) return \`\${Math.floor(diff / 60000)}m ago\`;
            if (diff < 86400000) return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
            return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
        };
        
        const getStatusColor = (status) => {
            const colors = {
                'new': 'info',
                'active': 'success',
                'pending': 'warning',
                'resolved': 'secondary',
                'closed': 'secondary'
            };
            return colors[status] || 'secondary';
        };
        
        onMounted(async () => {
            await loadConversation();
            await loadMessages();
            connectWebSocket();
        });
        
        onUnmounted(() => {
            if (websocket) {
                websocket.close();
            }
        });
        
        return {
            conversation,
            messages,
            loadingMessages,
            sendingMessage,
            newMessage,
            selectedFile,
            showImageModalFlag,
            modalImageUrl,
            messagesContainer,
            sendMessage,
            handleFileSelect,
            clearSelectedFile,
            deleteMessage,
            updateConversationStatus,
            showImageModal,
            formatMessageTime,
            getStatusColor
        };
    }
};
