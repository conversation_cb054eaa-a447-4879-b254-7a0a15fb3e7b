const DashboardComponent = {
    template: `
        <div class="dashboard">
            <div class="dashboard-header">
                <h1><i class="fas fa-tachometer-alt"></i> Dashboard</h1>
                <p>Welcome back, {{ user?.full_name }}! Here's your overview.</p>
            </div>

            <!-- Stats Cards -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-comments"></i>
                    </div>
                    <div class="stat-content">
                        <h3>{{ stats.totalConversations }}</h3>
                        <p>Total Conversations</p>
                        <span class="stat-change positive" v-if="stats.conversationsChange > 0">
                            <i class="fas fa-arrow-up"></i> +{{ stats.conversationsChange }}%
                        </span>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="stat-content">
                        <h3>{{ stats.activeConversations }}</h3>
                        <p>Active Conversations</p>
                        <span class="stat-badge">{{ stats.unassignedConversations }} unassigned</span>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stat-content">
                        <h3>{{ stats.totalCustomers }}</h3>
                        <p>Total Customers</p>
                        <span class="stat-change positive" v-if="stats.customersChange > 0">
                            <i class="fas fa-arrow-up"></i> +{{ stats.customersChange }}%
                        </span>
                    </div>
                </div>

                <div class="stat-card" v-if="user?.role === 'admin'">
                    <div class="stat-icon">
                        <i class="fas fa-user-friends"></i>
                    </div>
                    <div class="stat-content">
                        <h3>{{ stats.totalTeams }}</h3>
                        <p>Active Teams</p>
                        <span class="stat-badge">{{ stats.totalAgents }} agents</span>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="quick-actions">
                <h2><i class="fas fa-bolt"></i> Quick Actions</h2>
                <div class="actions-grid">
                    <router-link to="/conversations" class="action-card">
                        <i class="fas fa-comments"></i>
                        <h3>View Conversations</h3>
                        <p>Manage customer conversations</p>
                    </router-link>

                    <router-link to="/customers" class="action-card">
                        <i class="fas fa-user-plus"></i>
                        <h3>Add Customer</h3>
                        <p>Create new customer record</p>
                    </router-link>

                    <router-link to="/teams" class="action-card" v-if="user?.role === 'admin'">
                        <i class="fas fa-users-cog"></i>
                        <h3>Manage Teams</h3>
                        <p>Configure teams and agents</p>
                    </router-link>

                    <router-link to="/organizations" class="action-card" v-if="user?.role === 'admin'">
                        <i class="fas fa-building"></i>
                        <h3>Organizations</h3>
                        <p>Manage organizations</p>
                    </router-link>
                </div>
            </div>

            <!-- Recent Activity -->
            <div class="recent-activity">
                <div class="card">
                    <div class="card-header">
                        <h2><i class="fas fa-history"></i> Recent Activity</h2>
                        <button @click="refreshData" class="btn btn-sm btn-secondary">
                            <i class="fas fa-sync-alt" :class="{ 'fa-spin': loading }"></i>
                            Refresh
                        </button>
                    </div>

                    <div v-if="loading" class="loading-state">
                        <i class="fas fa-spinner fa-spin"></i>
                        <p>Loading recent activity...</p>
                    </div>

                    <div v-else-if="recentConversations.length === 0" class="empty-state">
                        <i class="fas fa-inbox"></i>
                        <p>No recent conversations</p>
                    </div>

                    <div v-else class="activity-list">
                        <div 
                            v-for="conversation in recentConversations" 
                            :key="conversation.id"
                            class="activity-item"
                            @click="viewConversation(conversation.id)"
                        >
                            <div class="activity-icon">
                                <i class="fas fa-comment"></i>
                            </div>
                            <div class="activity-content">
                                <h4>{{ conversation.customer?.name || 'Unknown Customer' }}</h4>
                                <p>{{ conversation.customer?.email || 'No email' }}</p>
                                <span class="activity-time">
                                    {{ formatTime(conversation.created_at) }}
                                </span>
                            </div>
                            <div class="activity-status">
                                <span :class="['badge', `badge-${getStatusColor(conversation.status)}`]">
                                    {{ conversation.status }}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `,
    
    setup() {
        const { ref, reactive, computed, onMounted } = Vue;
        const router = VueRouter.useRouter();
        
        const loading = ref(false);
        const user = computed(() => globalState.user);
        
        const stats = reactive({
            totalConversations: 0,
            activeConversations: 0,
            unassignedConversations: 0,
            totalCustomers: 0,
            totalTeams: 0,
            totalAgents: 0,
            conversationsChange: 0,
            customersChange: 0
        });
        
        const recentConversations = ref([]);
        
        const loadDashboardData = async () => {
            loading.value = true;
            try {
                // Load conversations
                const conversations = await api.getConversations();
                stats.totalConversations = conversations.length;
                stats.activeConversations = conversations.filter(c => c.status === 'active').length;
                stats.unassignedConversations = conversations.filter(c => !c.team_id).length;
                
                // Get recent conversations (last 10)
                recentConversations.value = conversations
                    .sort((a, b) => new Date(b.created_at) - new Date(a.created_at))
                    .slice(0, 10);
                
                // Load customers
                const customers = await api.getCustomers();
                stats.totalCustomers = customers.length;
                
                // Load teams and organizations (admin only)
                if (user.value?.role === 'admin') {
                    const teams = await api.getTeams();
                    stats.totalTeams = teams.length;
                    
                    const users = await api.getUsers();
                    stats.totalAgents = users.filter(u => u.role === 'agent').length;
                }
                
                // Mock percentage changes (in real app, compare with previous period)
                stats.conversationsChange = Math.floor(Math.random() * 20) + 5;
                stats.customersChange = Math.floor(Math.random() * 15) + 3;
                
            } catch (error) {
                console.error('Error loading dashboard data:', error);
                showToast('Failed to load dashboard data', 'error');
            } finally {
                loading.value = false;
            }
        };
        
        const refreshData = () => {
            loadDashboardData();
        };
        
        const viewConversation = (conversationId) => {
            router.push(`/conversations/${conversationId}`);
        };
        
        const formatTime = (timestamp) => {
            const date = new Date(timestamp);
            const now = new Date();
            const diff = now - date;
            
            if (diff < 60000) return 'Just now';
            if (diff < 3600000) return `${Math.floor(diff / 60000)}m ago`;
            if (diff < 86400000) return `${Math.floor(diff / 3600000)}h ago`;
            return `${Math.floor(diff / 86400000)}d ago`;
        };
        
        const getStatusColor = (status) => {
            const colors = {
                'new': 'info',
                'active': 'success',
                'pending': 'warning',
                'resolved': 'secondary',
                'closed': 'secondary'
            };
            return colors[status] || 'secondary';
        };
        
        onMounted(() => {
            loadDashboardData();
        });
        
        return {
            loading,
            user,
            stats,
            recentConversations,
            refreshData,
            viewConversation,
            formatTime,
            getStatusColor
        };
    }
};
