const OrganizationsComponent = {
    template: `
        <div class="organizations">
            <div class="page-header">
                <div class="header-content">
                    <h1><i class="fas fa-building"></i> Organizations</h1>
                    <p>Manage organizations and their settings</p>
                </div>
                <div class="header-actions">
                    <button @click="refreshOrganizations" class="btn btn-secondary">
                        <i class="fas fa-sync-alt" :class="{ 'fa-spin': loading }"></i>
                        Refresh
                    </button>
                    <button @click="showCreateModal = true" class="btn btn-primary">
                        <i class="fas fa-plus"></i>
                        Create Organization
                    </button>
                </div>
            </div>

            <!-- Organizations Grid -->
            <div v-if="loading" class="loading-state">
                <i class="fas fa-spinner fa-spin"></i>
                <p>Loading organizations...</p>
            </div>

            <div v-else-if="organizations.length === 0" class="empty-state">
                <i class="fas fa-building"></i>
                <p>No organizations found</p>
                <button @click="showCreateModal = true" class="btn btn-primary">
                    <i class="fas fa-plus"></i>
                    Create First Organization
                </button>
            </div>

            <div v-else class="organizations-grid">
                <div v-for="org in organizations" :key="org.id" class="org-card">
                    <div class="org-header">
                        <div class="org-info">
                            <h3>{{ org.name }}</h3>
                            <p>{{ org.description }}</p>
                        </div>
                        <div class="org-actions">
                            <button @click="editOrganization(org)" class="btn btn-sm btn-secondary">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button @click="deleteOrganization(org)" class="btn btn-sm btn-danger">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>

                    <div class="org-stats">
                        <div class="stat-item">
                            <i class="fas fa-user-friends"></i>
                            <span>{{ getOrgTeamCount(org.id) }} Teams</span>
                        </div>
                        <div class="stat-item">
                            <i class="fas fa-comments"></i>
                            <span>{{ getOrgConversationCount(org.id) }} Conversations</span>
                        </div>
                        <div class="stat-item">
                            <i class="fas fa-users"></i>
                            <span>{{ getOrgUserCount(org.id) }} Users</span>
                        </div>
                    </div>

                    <div class="org-details">
                        <div class="detail-row" v-if="org.website">
                            <i class="fas fa-globe"></i>
                            <a :href="org.website" target="_blank">{{ org.website }}</a>
                        </div>
                        <div class="detail-row" v-if="org.contact_email">
                            <i class="fas fa-envelope"></i>
                            <span>{{ org.contact_email }}</span>
                        </div>
                        <div class="detail-row" v-if="org.phone">
                            <i class="fas fa-phone"></i>
                            <span>{{ org.phone }}</span>
                        </div>
                        <div class="detail-row" v-if="org.address">
                            <i class="fas fa-map-marker-alt"></i>
                            <span>{{ org.address }}</span>
                        </div>
                    </div>

                    <div class="org-teams" v-if="getOrgTeams(org.id).length > 0">
                        <h4>Teams</h4>
                        <div class="teams-list">
                            <div v-for="team in getOrgTeams(org.id)" :key="team.id" class="team-item">
                                <i class="fas fa-users"></i>
                                <span>{{ team.name }}</span>
                                <span class="team-member-count">{{ getTeamMemberCount(team.id) }} members</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Create/Edit Organization Modal -->
            <div v-if="showCreateModal || showEditModal" class="modal-overlay" @click="closeModal">
                <div class="modal modal-lg" @click.stop>
                    <div class="modal-header">
                        <h3>
                            <i :class="showCreateModal ? 'fas fa-plus' : 'fas fa-edit'"></i>
                            {{ showCreateModal ? 'Create New Organization' : 'Edit Organization' }}
                        </h3>
                        <button @click="closeModal" class="modal-close">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    
                    <form @submit.prevent="saveOrganization" class="modal-body">
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">Organization Name *</label>
                                <input 
                                    v-model="orgForm.name" 
                                    type="text" 
                                    class="form-control" 
                                    placeholder="e.g., Acme Corporation"
                                    required
                                >
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">Description</label>
                            <textarea 
                                v-model="orgForm.description" 
                                class="form-control" 
                                rows="3"
                                placeholder="Brief description of the organization"
                            ></textarea>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">Website</label>
                                <input 
                                    v-model="orgForm.website" 
                                    type="url" 
                                    class="form-control" 
                                    placeholder="https://example.com"
                                >
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">Contact Email</label>
                                <input 
                                    v-model="orgForm.contact_email" 
                                    type="email" 
                                    class="form-control" 
                                    placeholder="<EMAIL>"
                                >
                            </div>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">Phone</label>
                                <input 
                                    v-model="orgForm.phone" 
                                    type="tel" 
                                    class="form-control" 
                                    placeholder="+1234567890"
                                >
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">Address</label>
                            <textarea 
                                v-model="orgForm.address" 
                                class="form-control" 
                                rows="2"
                                placeholder="Full address"
                            ></textarea>
                        </div>
                        
                        <div class="modal-footer">
                            <button type="button" @click="closeModal" class="btn btn-secondary">
                                Cancel
                            </button>
                            <button type="submit" class="btn btn-primary" :disabled="savingOrganization">
                                <i v-if="savingOrganization" class="fas fa-spinner fa-spin"></i>
                                <i v-else :class="showCreateModal ? 'fas fa-plus' : 'fas fa-save'"></i>
                                {{ savingOrganization ? 'Saving...' : (showCreateModal ? 'Create Organization' : 'Save Changes') }}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    `,
    
    setup() {
        const { ref, reactive, onMounted } = Vue;
        
        const loading = ref(false);
        const savingOrganization = ref(false);
        const showCreateModal = ref(false);
        const showEditModal = ref(false);
        
        const organizations = ref([]);
        const teams = ref([]);
        const users = ref([]);
        const conversations = ref([]);
        
        const orgForm = reactive({
            id: null,
            name: '',
            description: '',
            website: '',
            contact_email: '',
            phone: '',
            address: ''
        });
        
        const loadData = async () => {
            loading.value = true;
            try {
                const [organizationsData, teamsData, usersData, conversationsData] = await Promise.all([
                    api.getOrganizations(),
                    api.getTeams(),
                    api.getUsers(),
                    api.getConversations()
                ]);
                
                organizations.value = organizationsData;
                teams.value = teamsData;
                users.value = usersData;
                conversations.value = conversationsData;
                
            } catch (error) {
                console.error('Error loading organizations data:', error);
                showToast('Failed to load organizations', 'error');
            } finally {
                loading.value = false;
            }
        };
        
        const refreshOrganizations = () => {
            loadData();
        };
        
        const getOrgTeamCount = (orgId) => {
            return teams.value.filter(team => team.organization_id === orgId).length;
        };
        
        const getOrgConversationCount = (orgId) => {
            return conversations.value.filter(conv => conv.organization_id === orgId).length;
        };
        
        const getOrgUserCount = (orgId) => {
            const orgTeams = teams.value.filter(team => team.organization_id === orgId);
            const teamIds = orgTeams.map(team => team.id);
            return users.value.filter(user => teamIds.includes(user.team_id)).length;
        };
        
        const getOrgTeams = (orgId) => {
            return teams.value.filter(team => team.organization_id === orgId);
        };
        
        const getTeamMemberCount = (teamId) => {
            return users.value.filter(user => user.team_id === teamId).length;
        };
        
        const resetForm = () => {
            orgForm.id = null;
            orgForm.name = '';
            orgForm.description = '';
            orgForm.website = '';
            orgForm.contact_email = '';
            orgForm.phone = '';
            orgForm.address = '';
        };
        
        const closeModal = () => {
            showCreateModal.value = false;
            showEditModal.value = false;
            resetForm();
        };
        
        const editOrganization = (org) => {
            orgForm.id = org.id;
            orgForm.name = org.name;
            orgForm.description = org.description || '';
            orgForm.website = org.website || '';
            orgForm.contact_email = org.contact_email || '';
            orgForm.phone = org.phone || '';
            orgForm.address = org.address || '';
            showEditModal.value = true;
        };
        
        const saveOrganization = async () => {
            savingOrganization.value = true;
            try {
                const orgData = {
                    name: orgForm.name,
                    description: orgForm.description || null,
                    website: orgForm.website || null,
                    contact_email: orgForm.contact_email || null,
                    phone: orgForm.phone || null,
                    address: orgForm.address || null
                };
                
                if (showCreateModal.value) {
                    await api.createOrganization(orgData);
                    showToast('Organization created successfully', 'success');
                } else {
                    await api.updateOrganization(orgForm.id, orgData);
                    showToast('Organization updated successfully', 'success');
                }
                
                closeModal();
                loadData();
                
            } catch (error) {
                console.error('Error saving organization:', error);
                showToast('Failed to save organization', 'error');
            } finally {
                savingOrganization.value = false;
            }
        };
        
        const deleteOrganization = async (org) => {
            const teamCount = getOrgTeamCount(org.id);
            const conversationCount = getOrgConversationCount(org.id);
            
            let confirmMessage = `Are you sure you want to delete organization "${org.name}"?`;
            if (teamCount > 0 || conversationCount > 0) {
                confirmMessage += `\n\nThis will also affect:\n- ${teamCount} teams\n- ${conversationCount} conversations`;
            }
            
            if (!confirm(confirmMessage)) {
                return;
            }
            
            try {
                await api.deleteOrganization(org.id);
                showToast('Organization deleted successfully', 'success');
                loadData();
            } catch (error) {
                console.error('Error deleting organization:', error);
                showToast('Failed to delete organization', 'error');
            }
        };
        
        onMounted(() => {
            loadData();
        });
        
        return {
            loading,
            savingOrganization,
            showCreateModal,
            showEditModal,
            organizations,
            teams,
            users,
            orgForm,
            refreshOrganizations,
            getOrgTeamCount,
            getOrgConversationCount,
            getOrgUserCount,
            getOrgTeams,
            getTeamMemberCount,
            closeModal,
            editOrganization,
            saveOrganization,
            deleteOrganization
        };
    }
};
