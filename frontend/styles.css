/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #0d6efd;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #0dcaf0;
    --light-color: #f8f9fa;
    --dark-color: #212529;
    --border-color: #dee2e6;
    --shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);
}

body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    background-color: #f5f5f5;
    color: var(--dark-color);
    line-height: 1.6;
}

/* Navigation */
.navbar {
    background: white;
    box-shadow: var(--shadow);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    height: 60px;
}

.nav-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    height: 100%;
}

.nav-brand {
    display: flex;
    align-items: center;
    font-size: 1.5rem;
    font-weight: bold;
    color: var(--primary-color);
}

.nav-brand i {
    margin-right: 10px;
}

.nav-menu {
    display: flex;
    gap: 20px;
}

.nav-link {
    text-decoration: none;
    color: var(--dark-color);
    padding: 8px 16px;
    border-radius: 6px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.nav-link:hover,
.nav-link.router-link-active {
    background-color: var(--primary-color);
    color: white;
}

.nav-user {
    display: flex;
    align-items: center;
    gap: 15px;
}

.user-info {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
}

.user-name {
    font-weight: 600;
    font-size: 0.9rem;
}

.user-role {
    font-size: 0.8rem;
    color: var(--secondary-color);
    text-transform: capitalize;
}

.logout-btn {
    background: var(--danger-color);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.logout-btn:hover {
    background: #c82333;
}

/* Main Content */
.main-content {
    margin-top: 60px;
    padding: 20px;
    min-height: calc(100vh - 60px);
}

.main-content.no-nav {
    margin-top: 0;
    min-height: 100vh;
}

/* Cards */
.card {
    background: white;
    border-radius: 8px;
    box-shadow: var(--shadow);
    padding: 20px;
    margin-bottom: 20px;
}

.card-header {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid var(--border-color);
}

.card-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin: 0;
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    text-decoration: none;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.3s ease;
    text-align: center;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background: #0b5ed7;
}

.btn-success {
    background: var(--success-color);
    color: white;
}

.btn-success:hover {
    background: #157347;
}

.btn-danger {
    background: var(--danger-color);
    color: white;
}

.btn-danger:hover {
    background: #c82333;
}

.btn-secondary {
    background: var(--secondary-color);
    color: white;
}

.btn-secondary:hover {
    background: #5c636a;
}

.btn-sm {
    padding: 6px 12px;
    font-size: 0.8rem;
}

/* Forms */
.form-group {
    margin-bottom: 20px;
}

.form-label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: var(--dark-color);
}

.form-control {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    font-size: 0.9rem;
    transition: border-color 0.3s ease;
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.form-select {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m1 6 7 7 7-7'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 0.75rem center;
    background-size: 16px 12px;
    padding-right: 2.5rem;
}

/* Tables */
.table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
}

.table th,
.table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

.table th {
    background-color: var(--light-color);
    font-weight: 600;
    color: var(--dark-color);
}

.table tbody tr:hover {
    background-color: #f8f9fa;
}

/* Status badges */
.badge {
    display: inline-block;
    padding: 4px 8px;
    font-size: 0.75rem;
    font-weight: 500;
    border-radius: 4px;
    text-transform: uppercase;
}

.badge-success {
    background: var(--success-color);
    color: white;
}

.badge-warning {
    background: var(--warning-color);
    color: var(--dark-color);
}

.badge-danger {
    background: var(--danger-color);
    color: white;
}

.badge-info {
    background: var(--info-color);
    color: white;
}

.badge-secondary {
    background: var(--secondary-color);
    color: white;
}

/* Loading */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.loading-spinner {
    background: white;
    padding: 30px;
    border-radius: 8px;
    text-align: center;
}

.loading-spinner i {
    font-size: 2rem;
    color: var(--primary-color);
    margin-bottom: 10px;
}

/* Toast Notifications */
.toast-container {
    position: fixed;
    top: 80px;
    right: 20px;
    z-index: 1050;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.toast {
    background: white;
    border-radius: 6px;
    box-shadow: var(--shadow-lg);
    padding: 15px;
    display: flex;
    align-items: center;
    gap: 10px;
    min-width: 300px;
    cursor: pointer;
    animation: slideIn 0.3s ease;
}

.toast-success {
    border-left: 4px solid var(--success-color);
}

.toast-error {
    border-left: 4px solid var(--danger-color);
}

.toast-warning {
    border-left: 4px solid var(--warning-color);
}

.toast-info {
    border-left: 4px solid var(--info-color);
}

.toast-close {
    background: none;
    border: none;
    font-size: 1.2rem;
    cursor: pointer;
    margin-left: auto;
    color: var(--secondary-color);
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Responsive */
/* Dashboard Styles */
.dashboard-header {
    margin-bottom: 30px;
}

.dashboard-header h1 {
    color: var(--dark-color);
    margin-bottom: 5px;
}

.dashboard-header p {
    color: var(--secondary-color);
    margin: 0;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: var(--shadow);
    display: flex;
    align-items: center;
    gap: 15px;
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    background: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
}

.stat-content h3 {
    font-size: 2rem;
    font-weight: bold;
    margin: 0 0 5px 0;
    color: var(--dark-color);
}

.stat-content p {
    margin: 0 0 5px 0;
    color: var(--secondary-color);
    font-size: 0.9rem;
}

.stat-change {
    font-size: 0.8rem;
    font-weight: 500;
}

.stat-change.positive {
    color: var(--success-color);
}

.stat-badge {
    background: var(--light-color);
    color: var(--secondary-color);
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 0.75rem;
}

.quick-actions {
    margin-bottom: 30px;
}

.quick-actions h2 {
    margin-bottom: 20px;
    color: var(--dark-color);
}

.actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.action-card {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: var(--shadow);
    text-decoration: none;
    color: var(--dark-color);
    transition: all 0.3s ease;
    text-align: center;
}

.action-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    color: var(--primary-color);
}

.action-card i {
    font-size: 2rem;
    color: var(--primary-color);
    margin-bottom: 10px;
}

.action-card h3 {
    margin: 0 0 5px 0;
    font-size: 1.1rem;
}

.action-card p {
    margin: 0;
    color: var(--secondary-color);
    font-size: 0.9rem;
}

.activity-list {
    max-height: 400px;
    overflow-y: auto;
}

.activity-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    border-bottom: 1px solid var(--border-color);
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.activity-item:hover {
    background-color: var(--light-color);
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    background: var(--info-color);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}

.activity-content {
    flex: 1;
}

.activity-content h4 {
    margin: 0 0 2px 0;
    font-size: 0.9rem;
    color: var(--dark-color);
}

.activity-content p {
    margin: 0 0 2px 0;
    font-size: 0.8rem;
    color: var(--secondary-color);
}

.activity-time {
    font-size: 0.75rem;
    color: var(--secondary-color);
}

.loading-state,
.empty-state {
    text-align: center;
    padding: 40px 20px;
    color: var(--secondary-color);
}

.loading-state i,
.empty-state i {
    font-size: 2rem;
    margin-bottom: 10px;
    display: block;
}

@media (max-width: 768px) {
    .nav-container {
        padding: 0 15px;
    }

    .nav-menu {
        display: none;
    }

    .main-content {
        padding: 15px;
    }

    .card {
        padding: 15px;
    }

    .table {
        font-size: 0.8rem;
    }

    .toast-container {
        right: 15px;
        left: 15px;
    }

    .toast {
        min-width: auto;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .actions-grid {
        grid-template-columns: 1fr;
    }
}

/* Page Header */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 30px;
    gap: 20px;
}

.header-content h1 {
    color: var(--dark-color);
    margin-bottom: 5px;
}

.header-content p {
    color: var(--secondary-color);
    margin: 0;
}

.header-actions {
    display: flex;
    gap: 10px;
    flex-shrink: 0;
}

/* Filters */
.filters-card {
    margin-bottom: 20px;
}

.filters-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.filter-group {
    display: flex;
    flex-direction: column;
}

/* Tables */
.table-responsive {
    overflow-x: auto;
}

.customer-info,
.user-info {
    display: flex;
    align-items: center;
    gap: 10px;
}

.customer-avatar,
.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
}

.action-buttons {
    display: flex;
    gap: 5px;
}

.team-badge {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    background: var(--light-color);
    color: var(--secondary-color);
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
}

.team-badge.unassigned {
    background: var(--warning-color);
    color: var(--dark-color);
}

/* Pagination */
.pagination-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    border-top: 1px solid var(--border-color);
}

.pagination {
    display: flex;
    align-items: center;
    gap: 10px;
}

.pagination-current {
    padding: 0 15px;
    font-weight: 500;
}

.pagination-info {
    color: var(--secondary-color);
    font-size: 0.9rem;
}

/* Modal */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1050;
    padding: 20px;
}

.modal {
    background: white;
    border-radius: 8px;
    box-shadow: var(--shadow-lg);
    width: 100%;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-lg {
    max-width: 800px;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid var(--border-color);
}

.modal-header h3 {
    margin: 0;
    display: flex;
    align-items: center;
    gap: 10px;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--secondary-color);
    padding: 5px;
}

.modal-close:hover {
    color: var(--danger-color);
}

.modal-body {
    padding: 20px;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    padding: 20px;
    border-top: 1px solid var(--border-color);
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.form-check-label {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
}

.form-check-input {
    margin: 0;
}

/* Conversations */
.conversations-list {
    max-height: 600px;
    overflow-y: auto;
}

.conversation-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    border-bottom: 1px solid var(--border-color);
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.conversation-item:hover {
    background-color: var(--light-color);
}

.conversation-item:last-child {
    border-bottom: none;
}

.conversation-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: var(--info-color);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
}

.conversation-content {
    flex: 1;
}

.conversation-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 5px;
}

.conversation-header h4 {
    margin: 0;
    font-size: 1rem;
    color: var(--dark-color);
}

.conversation-time {
    font-size: 0.8rem;
    color: var(--secondary-color);
}

.conversation-details {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.customer-email {
    margin: 0;
    font-size: 0.9rem;
    color: var(--secondary-color);
}

.conversation-meta {
    display: flex;
    align-items: center;
    gap: 10px;
}

.conversation-actions {
    display: flex;
    gap: 5px;
}

/* Teams */
.teams-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 20px;
}

.team-card {
    background: white;
    border-radius: 8px;
    box-shadow: var(--shadow);
    padding: 20px;
}

.team-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 15px;
}

.team-info h3 {
    margin: 0 0 5px 0;
    color: var(--dark-color);
}

.team-info p {
    margin: 0;
    color: var(--secondary-color);
    font-size: 0.9rem;
}

.team-actions {
    display: flex;
    gap: 5px;
}

.team-stats {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid var(--border-color);
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 0.9rem;
    color: var(--secondary-color);
}

.team-members h4 {
    margin: 0 0 10px 0;
    font-size: 1rem;
    color: var(--dark-color);
}

.no-members {
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--secondary-color);
    font-style: italic;
    margin-bottom: 10px;
}

.members-list {
    margin-bottom: 15px;
}

.member-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 8px;
    border-radius: 6px;
    margin-bottom: 5px;
    background: var(--light-color);
}

.member-avatar {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.9rem;
}

.member-info {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.member-info strong {
    font-size: 0.9rem;
    color: var(--dark-color);
}

.member-role {
    font-size: 0.8rem;
    color: var(--secondary-color);
    text-transform: capitalize;
}

.add-member-btn {
    width: 100%;
}

/* Organizations */
.organizations-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 20px;
}

.org-card {
    background: white;
    border-radius: 8px;
    box-shadow: var(--shadow);
    padding: 20px;
}

.org-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 15px;
}

.org-info h3 {
    margin: 0 0 5px 0;
    color: var(--dark-color);
}

.org-info p {
    margin: 0;
    color: var(--secondary-color);
    font-size: 0.9rem;
}

.org-actions {
    display: flex;
    gap: 5px;
}

.org-stats {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid var(--border-color);
}

.org-details {
    margin-bottom: 20px;
}

.detail-row {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
    font-size: 0.9rem;
    color: var(--secondary-color);
}

.detail-row a {
    color: var(--primary-color);
    text-decoration: none;
}

.detail-row a:hover {
    text-decoration: underline;
}

.org-teams h4 {
    margin: 0 0 10px 0;
    font-size: 1rem;
    color: var(--dark-color);
}

.teams-list {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.team-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px;
    background: var(--light-color);
    border-radius: 4px;
    font-size: 0.9rem;
}

.team-member-count {
    margin-left: auto;
    color: var(--secondary-color);
    font-size: 0.8rem;
}

/* Chat Component */
.chat-container {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 100px);
    background: white;
    border-radius: 8px;
    box-shadow: var(--shadow);
    overflow: hidden;
}

.chat-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid var(--border-color);
    background: var(--light-color);
}

.chat-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

.conversation-details h2 {
    margin: 0 0 5px 0;
    font-size: 1.2rem;
    color: var(--dark-color);
}

.conversation-details p {
    margin: 0 0 5px 0;
    color: var(--secondary-color);
    font-size: 0.9rem;
}

.chat-actions {
    display: flex;
    align-items: center;
    gap: 10px;
}

.chat-body {
    display: flex;
    flex-direction: column;
    flex: 1;
    overflow: hidden;
}

.messages-container {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
}

.empty-messages {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: var(--secondary-color);
    text-align: center;
}

.empty-messages i {
    font-size: 3rem;
    margin-bottom: 15px;
}

.messages-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.message {
    display: flex;
    gap: 10px;
    max-width: 80%;
}

.message-customer {
    align-self: flex-start;
}

.message-agent {
    align-self: flex-end;
    flex-direction: row-reverse;
}

.message-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
    flex-shrink: 0;
}

.message-customer .message-avatar {
    background: var(--info-color);
}

.message-agent .message-avatar {
    background: var(--primary-color);
}

.message-content {
    flex: 1;
    min-width: 0;
}

.message-agent .message-content {
    text-align: right;
}

.message-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 5px;
    font-size: 0.8rem;
    color: var(--secondary-color);
}

.message-agent .message-header {
    flex-direction: row-reverse;
}

.message-sender {
    font-weight: 500;
}

.message-body {
    background: var(--light-color);
    padding: 10px 15px;
    border-radius: 12px;
    word-wrap: break-word;
}

.message-agent .message-body {
    background: var(--primary-color);
    color: white;
}

.message-text {
    line-height: 1.4;
}

.message-file {
    display: flex;
    align-items: center;
    gap: 8px;
}

.message-file a {
    color: inherit;
    text-decoration: none;
}

.message-file a:hover {
    text-decoration: underline;
}

.message-image img {
    max-width: 200px;
    max-height: 200px;
    border-radius: 8px;
    cursor: pointer;
    transition: transform 0.3s ease;
}

.message-image img:hover {
    transform: scale(1.05);
}

.message-actions {
    margin-top: 5px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.message:hover .message-actions {
    opacity: 1;
}

.message-input {
    padding: 20px;
    border-top: 1px solid var(--border-color);
    background: white;
}

.input-form {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.input-group {
    display: flex;
    gap: 10px;
}

.input-group .form-control {
    flex: 1;
}

.selected-file {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px;
    background: var(--light-color);
    border-radius: 6px;
    font-size: 0.9rem;
}

.selected-file span {
    flex: 1;
}

/* Image Modal */
.image-modal {
    position: relative;
    max-width: 90vw;
    max-height: 90vh;
    background: white;
    border-radius: 8px;
    padding: 20px;
}

.image-modal img {
    width: 100%;
    height: auto;
    max-width: 100%;
    max-height: 80vh;
    object-fit: contain;
}

.image-modal .modal-close {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(0, 0, 0, 0.5);
    color: white;
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
}

/* Responsive Design */
@media (max-width: 768px) {
    .page-header {
        flex-direction: column;
        align-items: stretch;
        gap: 15px;
    }

    .header-actions {
        justify-content: flex-end;
    }

    .filters-grid {
        grid-template-columns: 1fr;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .teams-grid,
    .organizations-grid {
        grid-template-columns: 1fr;
    }

    .conversation-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .conversation-header {
        width: 100%;
    }

    .conversation-actions {
        width: 100%;
        justify-content: flex-end;
    }

    .message {
        max-width: 95%;
    }

    .chat-header {
        flex-direction: column;
        align-items: stretch;
        gap: 15px;
    }

    .chat-info {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .pagination-container {
        flex-direction: column;
        gap: 10px;
        text-align: center;
    }
}
