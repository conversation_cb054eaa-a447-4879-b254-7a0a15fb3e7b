<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Yupcha Customer Bot AI - Admin Dashboard</title>
    
    <!-- Vue 3 CDN -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    
    <!-- Vue Router CDN -->
    <script src="https://unpkg.com/vue-router@4"></script>
    
    <!-- Axios for HTTP requests -->
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
    
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div id="app">
        <!-- Navigation Bar -->
        <nav class="navbar" v-if="isAuthenticated">
            <div class="nav-container">
                <div class="nav-brand">
                    <i class="fas fa-robot"></i>
                    <span>Yupcha AI</span>
                </div>
                
                <div class="nav-menu">
                    <router-link to="/dashboard" class="nav-link">
                        <i class="fas fa-tachometer-alt"></i> Dashboard
                    </router-link>
                    <router-link to="/conversations" class="nav-link">
                        <i class="fas fa-comments"></i> Conversations
                    </router-link>
                    <router-link to="/customers" class="nav-link">
                        <i class="fas fa-users"></i> Customers
                    </router-link>
                    <router-link to="/teams" class="nav-link" v-if="user.role === 'admin'">
                        <i class="fas fa-user-friends"></i> Teams
                    </router-link>
                    <router-link to="/organizations" class="nav-link" v-if="user.role === 'admin'">
                        <i class="fas fa-building"></i> Organizations
                    </router-link>
                    <router-link to="/users" class="nav-link" v-if="user.role === 'admin'">
                        <i class="fas fa-user-cog"></i> Users
                    </router-link>
                </div>
                
                <div class="nav-user">
                    <div class="user-info">
                        <span class="user-name">{{ user.full_name }}</span>
                        <span class="user-role">{{ user.role }}</span>
                    </div>
                    <button @click="logout" class="logout-btn">
                        <i class="fas fa-sign-out-alt"></i> Logout
                    </button>
                </div>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="main-content" :class="{ 'no-nav': !isAuthenticated }">
            <router-view></router-view>
        </main>

        <!-- Loading Overlay -->
        <div v-if="loading" class="loading-overlay">
            <div class="loading-spinner">
                <i class="fas fa-spinner fa-spin"></i>
                <p>Loading...</p>
            </div>
        </div>

        <!-- Toast Notifications -->
        <div class="toast-container">
            <div v-for="toast in toasts" :key="toast.id" 
                 :class="['toast', `toast-${toast.type}`]"
                 @click="removeToast(toast.id)">
                <i :class="getToastIcon(toast.type)"></i>
                <span>{{ toast.message }}</span>
                <button class="toast-close">&times;</button>
            </div>
        </div>
    </div>

    <!-- Load Vue components and app -->
    <script src="components/Login.js"></script>
    <script src="components/Dashboard.js"></script>
    <script src="components/Conversations.js"></script>
    <script src="components/Customers.js"></script>
    <script src="components/Teams.js"></script>
    <script src="components/Organizations.js"></script>
    <script src="components/Users.js"></script>
    <script src="components/Chat.js"></script>
    <script src="app.js"></script>
</body>
</html>
