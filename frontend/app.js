// Vue 3 Application
const { createApp, ref, reactive, computed, onMounted } = Vue;
const { createRouter, createWebHashHistory } = VueRouter;

// API Configuration
const API_BASE_URL = 'http://localhost:8000';

// Axios configuration
axios.defaults.baseURL = API_BASE_URL;
axios.defaults.withCredentials = true;

// Global state management
const globalState = reactive({
    user: null,
    isAuthenticated: false,
    loading: false,
    toasts: []
});

// Toast notification system
let toastId = 0;
const showToast = (message, type = 'info') => {
    const toast = {
        id: ++toastId,
        message,
        type
    };
    globalState.toasts.push(toast);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        removeToast(toast.id);
    }, 5000);
};

const removeToast = (id) => {
    const index = globalState.toasts.findIndex(t => t.id === id);
    if (index > -1) {
        globalState.toasts.splice(index, 1);
    }
};

// API service
const api = {
    // Authentication
    async login(email, password) {
        const response = await axios.post('/api/auth/login', 
            `username=${encodeURIComponent(email)}&password=${encodeURIComponent(password)}`,
            {
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded'
                }
            }
        );
        return response.data;
    },

    async logout() {
        const response = await axios.post('/api/auth/logout');
        return response.data;
    },

    async getCurrentUser() {
        const response = await axios.get('/api/auth/me');
        return response.data;
    },

    // Users
    async getUsers() {
        const response = await axios.get('/api/users/');
        return response.data;
    },

    async createUser(userData) {
        const response = await axios.post('/api/users/', userData);
        return response.data;
    },

    async updateUser(userId, userData) {
        const response = await axios.put(`/api/users/${userId}`, userData);
        return response.data;
    },

    async deleteUser(userId) {
        const response = await axios.delete(`/api/users/${userId}`);
        return response.data;
    },

    // Organizations
    async getOrganizations() {
        const response = await axios.get('/api/organizations/');
        return response.data;
    },

    async createOrganization(orgData) {
        const response = await axios.post('/api/organizations/', orgData);
        return response.data;
    },

    async updateOrganization(orgId, orgData) {
        const response = await axios.put(`/api/organizations/${orgId}`, orgData);
        return response.data;
    },

    async deleteOrganization(orgId) {
        const response = await axios.delete(`/api/organizations/${orgId}`);
        return response.data;
    },

    // Teams
    async getTeams() {
        const response = await axios.get('/api/teams/');
        return response.data;
    },

    async createTeam(teamData) {
        const response = await axios.post('/api/teams/', teamData);
        return response.data;
    },

    async updateTeam(teamId, teamData) {
        const response = await axios.put(`/api/teams/${teamId}`, teamData);
        return response.data;
    },

    async deleteTeam(teamId) {
        const response = await axios.delete(`/api/teams/${teamId}`);
        return response.data;
    },

    // Customers
    async getCustomers() {
        const response = await axios.get('/api/customers/');
        return response.data;
    },

    async createCustomer(customerData) {
        const response = await axios.post('/api/customers/', customerData);
        return response.data;
    },

    async updateCustomer(customerId, customerData) {
        const response = await axios.put(`/api/customers/${customerId}`, customerData);
        return response.data;
    },

    async deleteCustomer(customerId) {
        const response = await axios.delete(`/api/customers/${customerId}`);
        return response.data;
    },

    // Conversations
    async getConversations() {
        const response = await axios.get('/api/conversations/');
        return response.data;
    },

    async getConversation(conversationId) {
        const response = await axios.get(`/api/conversations/${conversationId}`);
        return response.data;
    },

    async createConversation(conversationData) {
        const response = await axios.post('/api/conversations/', conversationData);
        return response.data;
    },

    async assignTeamToConversation(conversationId, teamId) {
        const response = await axios.post(`/api/conversations/${conversationId}/assign/${teamId}`);
        return response.data;
    },

    async getConversationMessages(conversationId) {
        const response = await axios.get(`/api/conversations/${conversationId}/messages`);
        return response.data;
    },

    async getTeamConversations(teamId) {
        const response = await axios.get(`/api/conversations/team/${teamId}`);
        return response.data;
    },

    async getUnassignedConversations() {
        const response = await axios.get('/api/conversations/unassigned');
        return response.data;
    }
};

// Router configuration
const routes = [
    { path: '/', redirect: '/login' },
    { path: '/login', component: LoginComponent, meta: { requiresGuest: true } },
    { path: '/dashboard', component: DashboardComponent, meta: { requiresAuth: true } },
    { path: '/conversations', component: ConversationsComponent, meta: { requiresAuth: true } },
    { path: '/conversations/:id', component: ChatComponent, meta: { requiresAuth: true } },
    { path: '/customers', component: CustomersComponent, meta: { requiresAuth: true } },
    { path: '/teams', component: TeamsComponent, meta: { requiresAuth: true, requiresAdmin: true } },
    { path: '/organizations', component: OrganizationsComponent, meta: { requiresAuth: true, requiresAdmin: true } },
    { path: '/users', component: UsersComponent, meta: { requiresAuth: true, requiresAdmin: true } }
];

const router = createRouter({
    history: createWebHashHistory(),
    routes
});

// Navigation guards
router.beforeEach(async (to, from, next) => {
    // Check authentication status
    if (!globalState.isAuthenticated && to.meta.requiresAuth) {
        try {
            const user = await api.getCurrentUser();
            globalState.user = user;
            globalState.isAuthenticated = true;
        } catch (error) {
            next('/login');
            return;
        }
    }

    // Check admin access
    if (to.meta.requiresAdmin && globalState.user?.role !== 'admin') {
        showToast('Access denied. Admin privileges required.', 'error');
        next('/dashboard');
        return;
    }

    // Redirect authenticated users away from login
    if (to.meta.requiresGuest && globalState.isAuthenticated) {
        next('/dashboard');
        return;
    }

    next();
});

// Main Vue application
const app = createApp({
    setup() {
        const isAuthenticated = computed(() => globalState.isAuthenticated);
        const user = computed(() => globalState.user);
        const loading = computed(() => globalState.loading);
        const toasts = computed(() => globalState.toasts);

        const logout = async () => {
            try {
                globalState.loading = true;
                await api.logout();
                globalState.user = null;
                globalState.isAuthenticated = false;
                showToast('Logged out successfully', 'success');
                router.push('/login');
            } catch (error) {
                showToast('Logout failed', 'error');
            } finally {
                globalState.loading = false;
            }
        };

        const getToastIcon = (type) => {
            const icons = {
                success: 'fas fa-check-circle',
                error: 'fas fa-exclamation-circle',
                warning: 'fas fa-exclamation-triangle',
                info: 'fas fa-info-circle'
            };
            return icons[type] || icons.info;
        };

        // Check authentication on app load
        onMounted(async () => {
            try {
                const user = await api.getCurrentUser();
                globalState.user = user;
                globalState.isAuthenticated = true;
                if (router.currentRoute.value.path === '/login') {
                    router.push('/dashboard');
                }
            } catch (error) {
                // User not authenticated, stay on current route
            }
        });

        return {
            isAuthenticated,
            user,
            loading,
            toasts,
            logout,
            getToastIcon,
            removeToast
        };
    }
});

// Use router
app.use(router);

// Global properties
app.config.globalProperties.$api = api;
app.config.globalProperties.$showToast = showToast;
app.config.globalProperties.$globalState = globalState;

// Mount the app
app.mount('#app');
