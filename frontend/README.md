# Yupcha Customer Bot AI - Frontend

A complete Vue.js 3 administration dashboard for the Yupcha Customer Service Bot AI system.

## 🚀 Features

### **Authentication & Authorization**
- ✅ Secure login system with session management
- ✅ Role-based access control (Admin/Agent)
- ✅ Auto-logout and session validation

### **Dashboard**
- ✅ Real-time statistics and metrics
- ✅ Quick action shortcuts
- ✅ Recent activity feed
- ✅ Role-specific content

### **Conversation Management**
- ✅ View all customer conversations
- ✅ Filter by status, team, and search
- ✅ Real-time chat interface
- ✅ File sharing and image support
- ✅ Team assignment and status updates

### **Customer Management**
- ✅ Complete customer database
- ✅ Add, edit, and delete customers
- ✅ Search and filtering
- ✅ Conversation history tracking

### **Team Management** (Admin Only)
- ✅ Create and manage support teams
- ✅ Assign agents to teams
- ✅ Team performance metrics
- ✅ Organization association

### **Organization Management** (Admin Only)
- ✅ Multi-organization support
- ✅ Organization profiles and settings
- ✅ Team and user management
- ✅ Contact information management

### **User Management** (Admin Only)
- ✅ Add and manage system users
- ✅ Role assignment (Admin/Agent)
- ✅ Team assignments
- ✅ User activation/deactivation

## 🛠️ Technology Stack

- **Vue.js 3** - Progressive JavaScript framework
- **Vue Router 4** - Client-side routing
- **Axios** - HTTP client for API calls
- **WebSocket** - Real-time chat communication
- **Font Awesome** - Icon library
- **CSS Grid & Flexbox** - Modern responsive layouts

## 📁 Project Structure

```
frontend/
├── index.html              # Main HTML file
├── app.js                  # Vue app configuration and routing
├── styles.css              # Global styles and component styles
├── components/
│   ├── Login.js            # Authentication component
│   ├── Dashboard.js        # Main dashboard
│   ├── Conversations.js    # Conversation management
│   ├── Customers.js        # Customer management
│   ├── Teams.js           # Team management (Admin)
│   ├── Organizations.js   # Organization management (Admin)
│   ├── Users.js           # User management (Admin)
│   └── Chat.js            # Real-time chat interface
└── README.md              # This file
```

## 🚀 Getting Started

### **Prerequisites**
- Yupcha Customer Bot AI backend running on `http://0.0.0.0:8000`
- Modern web browser with JavaScript enabled

### **Installation**
1. **No build process required!** This is a pure client-side application using CDN resources.

2. **Start the backend server:**
   ```bash
   cd /path/to/yupcha-customerbot-ai
   uv run uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
   ```

3. **Open the frontend:**
   ```bash
   # Option 1: Open directly in browser
   open frontend/index.html
   
   # Option 2: Serve with a simple HTTP server
   cd frontend
   python -m http.server 8080
   # Then visit http://localhost:8080
   ```

### **Demo Credentials**
The login page includes quick-fill buttons for demo credentials:

**Admin Access:**
- Email: `<EMAIL>`
- Password: `adminpassword`

**Agent Access:**
- Email: `<EMAIL>`
- Password: `agentpassword`

## 🎯 Usage Guide

### **For Administrators**
1. **Login** with admin credentials
2. **Dashboard** - View system overview and metrics
3. **Organizations** - Manage company organizations
4. **Teams** - Create teams and assign agents
5. **Users** - Add new agents and manage permissions
6. **Customers** - View and manage customer database
7. **Conversations** - Monitor all customer interactions

### **For Agents**
1. **Login** with agent credentials
2. **Dashboard** - View personal metrics and team activity
3. **Conversations** - Handle assigned customer conversations
4. **Customers** - View customer information
5. **Chat** - Real-time communication with customers

## 🔧 Configuration

### **API Base URL**
The frontend is configured to connect to the backend at `http://0.0.0.0:8000`. To change this:

1. Open `frontend/app.js`
2. Modify the `API_BASE_URL` constant:
   ```javascript
   const API_BASE_URL = 'https://your-backend-domain.com';
   ```

### **WebSocket URL**
WebSocket connections are configured in the Chat component. To change the WebSocket URL:

1. Open `frontend/components/Chat.js`
2. Modify the `wsUrl` in the `connectWebSocket` function:
   ```javascript
   const wsUrl = `wss://your-backend-domain.com/api/ws/chat/${conversationId}`;
   ```

## 🎨 Customization

### **Styling**
- All styles are in `frontend/styles.css`
- Uses CSS custom properties (variables) for easy theming
- Responsive design with mobile-first approach

### **Components**
- Each component is self-contained in its own file
- Easy to modify or extend functionality
- Consistent API patterns across all components

## 🔒 Security Features

- **Session-based authentication** with HTTP-only cookies
- **CSRF protection** through credentials inclusion
- **Role-based access control** for admin features
- **Input validation** and sanitization
- **Secure file uploads** with type restrictions

## 📱 Responsive Design

- **Mobile-first** responsive design
- **Touch-friendly** interface elements
- **Adaptive layouts** for all screen sizes
- **Optimized performance** on mobile devices

## 🚀 Production Deployment

### **Static Hosting**
Since this is a pure client-side application, you can deploy it to any static hosting service:

- **Netlify**: Drag and drop the `frontend` folder
- **Vercel**: Connect your Git repository
- **GitHub Pages**: Push to a GitHub repository
- **AWS S3**: Upload files to an S3 bucket with static hosting
- **Any web server**: Copy files to your web server directory

### **Environment Configuration**
For production deployment:

1. Update the `API_BASE_URL` to your production backend
2. Ensure CORS is properly configured on your backend
3. Use HTTPS for both frontend and backend in production
4. Configure proper CSP headers for security

## 🐛 Troubleshooting

### **Common Issues**

**"Failed to load data" errors:**
- Check that the backend server is running on `http://0.0.0.0:8000`
- Verify the API_BASE_URL is correct
- Check browser console for CORS errors

**WebSocket connection failures:**
- Ensure WebSocket URL is correct
- Check that the backend supports WebSocket connections
- Verify firewall settings allow WebSocket traffic

**Authentication issues:**
- Clear browser cookies and try again
- Check that credentials are correct
- Verify backend authentication endpoints are working

## 📞 Support

For issues or questions:
1. Check the browser console for error messages
2. Verify backend API is responding correctly
3. Review the backend logs for any errors
4. Ensure all required backend services are running

## 🎉 Success!

Your complete Vue.js frontend is now ready! The application provides a full-featured administration interface for managing your customer service bot system with real-time chat capabilities, comprehensive user management, and a beautiful responsive design.
