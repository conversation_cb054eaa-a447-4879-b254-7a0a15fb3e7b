<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Yupcha Agent Dashboard</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --bg-main: #f0f2f5;
            --bg-panel: #ffffff;
            --bg-input: #f8f9fa;
            --bg-agent-msg: #007bff;
            --bg-customer-msg: #e9ecef;
            --bg-bot-msg: #6c757d;
            --text-dark: #212529;
            --text-light: #ffffff;
            --text-muted: #6c757d;
            --border-color: #dee2e6;
            --success: #198754;
            --danger: #dc3545;
            --info: #0dcaf0;
        }
        body { font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, sans-serif; margin: 0; padding: 20px; background-color: var(--bg-main); color: var(--text-dark); }
        .container { max-width: 1400px; margin: 0 auto; display: grid; grid-template-columns: 300px 1fr 350px; gap: 20px; }
        .panel { background: var(--bg-panel); border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.08); padding: 20px; display: flex; flex-direction: column; }
        .login-panel { grid-column: 1 / -1; }
        .chat-panel { grid-column: 2 / 3; }
        .convos-panel, .notifications-panel { display: flex; flex-direction: column; }
        h2, h3 { color: #333; border-bottom: 1px solid var(--border-color); padding-bottom: 10px; margin-top: 0; }
        .messages { flex: 1; overflow-y: auto; border: 1px solid var(--border-color); padding: 10px; margin-bottom: 15px; background-color: #fdfdfd; border-radius: 4px; display: flex; flex-direction: column; min-height: 400px; }
        .message { display: flex; flex-direction: column; margin-bottom: 12px; max-width: 80%; padding: 8px 12px; border-radius: 18px; line-height: 1.4; }
        .message .sender { font-size: 0.75rem; font-weight: bold; margin-bottom: 4px; text-transform: capitalize; }
        .message.agent, .message.admin { background-color: var(--bg-agent-msg); color: var(--text-light); align-self: flex-end; border-bottom-right-radius: 4px; }
        .message.customer { background-color: var(--bg-customer-msg); color: var(--text-dark); align-self: flex-start; border-bottom-left-radius: 4px; }
        .message.bot { background-color: var(--bg-bot-msg); color: var(--text-light); align-self: flex-start; border-bottom-left-radius: 4px; font-style: italic; }
        .message.system, .message.notification { background-color: #fff3cd; color: #664d03; font-style: italic; font-size: 0.8rem; align-self: center; white-space: pre-wrap; text-align: center; }
        .input-group { display: flex; gap: 10px; margin-top: auto; }
        input[type="text"], input[type="email"], input[type="password"] { flex: 1; padding: 10px; border: 1px solid var(--border-color); border-radius: 4px; }
        button { padding: 10px 15px; border: none; border-radius: 4px; cursor: pointer; color: white; background-color: var(--bg-agent-msg); font-weight: 500; transition: opacity 0.2s; }
        button:hover { opacity: 0.9; } button:disabled { background-color: var(--text-muted); cursor: not-allowed; }
        .status { font-weight: bold; margin-bottom: 10px; padding: 8px; border-radius: 4px; text-align: center; }
        .connected { color: var(--success); background-color: #d1e7dd; } .disconnected { color: var(--danger); background-color: #f8d7da; }
        #conversationsList { list-style-type: none; padding: 0; margin: 0; overflow-y: auto; flex: 1; }
        #conversationsList li { padding: 12px; border-bottom: 1px solid var(--border-color); cursor: pointer; transition: background-color 0.2s; }
        #conversationsList li:hover { background-color: #f1f3f5; }
        #conversationsList li.selected { background-color: #e0e7ff; font-weight: bold; }
        #conversationsList small { color: var(--text-muted); display: block; }
        .hidden { display: none; }
    </style>
</head>
<body>

    <div id="loginPanel" class="panel login-panel">
        <h2><i class="fas fa-sign-in-alt"></i> Agent Login</h2>
        <div class="input-group" style="margin-bottom: 10px;">
            <input type="email" id="loginEmail" placeholder="Email (e.g., <EMAIL>)" value="<EMAIL>">
            <input type="password" id="loginPassword" placeholder="Password" value="agentpassword">
        </div>
        <button onclick="login()">Login</button>
        <div id="loginStatus" style="margin-top: 10px;"></div>
    </div>

    <div id="dashboard" class="container hidden">
        <div class="panel convos-panel">
            <h2><i class="fas fa-comments"></i> Conversations</h2>
            <div id="agentInfo" class="status">Not Logged In</div>
            <button onclick="fetchConversations()">Fetch Conversations</button>
            <ul id="conversationsList"></ul>
        </div>

        <div class="panel chat-panel">
            <h2><i class="fas fa-comment-dots"></i> Chat: <span id="chatTitle">No Conversation Selected</span></h2>
            <div class="status disconnected" id="chatStatus">Disconnected</div>
            <div class="messages" id="chatMessages"></div>
            <div class="input-group">
                <input type="text" id="chatInput" placeholder="Type your message..." onkeypress="handleKey(event, sendMessage)" disabled>
                <button onclick="sendMessage()" id="sendButton" disabled>Send</button>
            </div>
        </div>
        
        <div class="panel notifications-panel">
            <h2><i class="fas fa-bell"></i> Notifications</h2>
            <div class="status disconnected" id="notificationStatus">Disconnected</div>
            <div class="messages" id="notificationMessages"></div>
        </div>
    </div>

    <script>
        const API_BASE_URL = "http://localhost:8000";
        const WS_BASE_URL = "ws://localhost:8000";

        let chatWs, notificationWs;
        let currentConversationId = null;

        // --- DOM Elements ---
        const getEl = (id) => document.getElementById(id);
        const loginPanel = getEl('loginPanel');
        const dashboard = getEl('dashboard');
        const loginStatus = getEl('loginStatus');
        const agentInfo = getEl('agentInfo');
        const conversationsList = getEl('conversationsList');
        const chatTitle = getEl('chatTitle');
        const chatStatus = getEl('chatStatus');
        const chatMessages = getEl('chatMessages');
        const chatInput = getEl('chatInput');
        const sendButton = getEl('sendButton');
        const notificationStatus = getEl('notificationStatus');
        const notificationMessages = getEl('notificationMessages');
        
        // --- Authentication ---
        async function login() {
            const email = getEl('loginEmail').value;
            const password = getEl('loginPassword').value;
            
            try {
                loginStatus.textContent = 'Logging in...';
                const response = await fetch(`${API_BASE_URL}/api/auth/login`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                    body: `username=${encodeURIComponent(email)}&password=${encodeURIComponent(password)}`,
                });
                if (!response.ok) throw new Error(`Login failed: ${await response.text()}`);
                
                loginStatus.textContent = 'Login successful! Fetching user details...';
                await fetchMe();
            } catch (error) {
                loginStatus.innerHTML = `<div class="status disconnected">Error: ${error.message}</div>`;
            }
        }

        async function fetchMe() {
            try {
                const response = await fetch(`${API_BASE_URL}/api/auth/me`);
                if (!response.ok) {
                    logout();
                    return;
                }
                const user = await response.json();
                loginPanel.classList.add('hidden');
                dashboard.classList.remove('hidden');
                agentInfo.textContent = `Logged in as: ${user.full_name} (${user.role})`;
                agentInfo.className = 'status connected';
                fetchConversations();
                connectNotificationWebSocket(user.id, user.organization_id);
            } catch (error) {
                logout();
            }
        }

        function logout() {
            fetch(`${API_BASE_URL}/api/auth/logout`, { method: 'POST' });
            if (chatWs) chatWs.close();
            if (notificationWs) notificationWs.close();
            loginPanel.classList.remove('hidden');
            dashboard.classList.add('hidden');
        }

        // --- Core Logic ---
        async function fetchConversations() {
            conversationsList.innerHTML = '<li>Loading...</li>';
            try {
                const response = await fetch(`${API_BASE_URL}/api/conversations/`);
                if (!response.ok) throw new Error('Failed to fetch conversations');
                const convos = await response.json();
                
                conversationsList.innerHTML = convos.length ? '' : '<li>No conversations found.</li>';
                convos.forEach(convo => {
                    const li = document.createElement('li');
                    li.textContent = `Conv #${convo.id} (Customer #${convo.customer_id})`;
                    li.onclick = () => selectConversation(convo.id);
                    conversationsList.appendChild(li);
                });
            } catch (error) {
                conversationsList.innerHTML = `<li>Error: ${error.message}</li>`;
            }
        }

        function selectConversation(id) {
            if (currentConversationId === id && chatWs && chatWs.readyState === WebSocket.OPEN) return;
            
            currentConversationId = id;
            document.querySelectorAll('#conversationsList li').forEach(li => li.classList.remove('selected'));
            event.target.classList.add('selected');
            
            chatTitle.textContent = `Conversation #${id}`;
            chatMessages.innerHTML = '';
            
            if (chatWs) chatWs.close();
            connectChatWebSocket(id);
        }

        // --- WebSocket Connections ---
        function connectChatWebSocket(convId) {
            const uri = `${WS_BASE_URL}/api/ws/chat/${convId}`;
            chatWs = new WebSocket(uri);

            chatWs.onopen = () => {
                chatStatus.textContent = 'Connected';
                chatStatus.className = 'status connected';
                chatInput.disabled = false;
                sendButton.disabled = false;
            };

            chatWs.onmessage = (event) => {
                const data = JSON.parse(event.data);
                renderMessage('chat', data);
            };

            chatWs.onclose = () => {
                chatStatus.textContent = 'Disconnected';
                chatStatus.className = 'status disconnected';
                chatInput.disabled = true;
                sendButton.disabled = true;
            };

            chatWs.onerror = () => {
                chatStatus.textContent = 'Connection Error';
                chatStatus.className = 'status disconnected';
            };
        }
        
        function connectNotificationWebSocket() {
            const uri = `${WS_BASE_URL}/api/ws/agent-notifications`;
            notificationWs = new WebSocket(uri);

            notificationWs.onopen = () => {
                notificationStatus.textContent = 'Connected';
                notificationStatus.className = 'status connected';
            };

            notificationWs.onmessage = (event) => {
                const data = JSON.parse(event.data);
                renderMessage('notification', data);
            };

            notificationWs.onclose = () => {
                notificationStatus.textContent = 'Disconnected';
                notificationStatus.className = 'status disconnected';
            };
        }

        // --- Message Handling ---
        function sendMessage() {
            const content = chatInput.value.trim();
            if (content && chatWs && chatWs.readyState === WebSocket.OPEN) {
                chatWs.send(JSON.stringify({ content }));
                chatInput.value = '';
            }
        }

        function renderMessage(panel, data) {
            const messagesEl = getEl(`${panel}Messages`);
            const msgEl = document.createElement('div');
            
            if (panel === 'notification' || data.type === 'system') {
                msgEl.className = 'message notification';
                msgEl.innerHTML = `<div><strong>${data.type || 'SYSTEM'}</strong>: ${data.detail || JSON.stringify(data, null, 2)}</div>`;
            } else {
                const senderClass = (data.sender === 'agent' || data.sender === 'admin') ? 'agent' : data.sender;
                msgEl.className = `message ${senderClass}`;
                msgEl.innerHTML = `<div class="sender">${data.sender}</div><div class="content">${data.content}</div>`;
            }
            
            messagesEl.appendChild(msgEl);
            messagesEl.scrollTop = messagesEl.scrollHeight;
        }

        function handleKey(event, sendFunction) {
            if (event.key === 'Enter') sendFunction();
        }

        // --- Initial Load ---
        window.onload = fetchMe;
    </script>
</body>
</html>