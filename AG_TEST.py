# yupcha-customerbot-ai/tests/demo_agent_chat.py

import asyncio
import websockets
import json
import uuid
import aioconsole
import aiohttp
import sys
from datetime import datetime

# --- Configuration ---
WS_BASE_URL = "ws://localhost:8002/api/v1/ws"
API_BASE_URL = "http://localhost:8002/api/v1"
AGENT_EMAIL = "<EMAIL>"
AGENT_PASSWORD = "adminpassword"
ORGANIZATION_ID = '06874bc0-ac71-75ea-8000-8666ab57b647'

# ANSI color codes for pretty printing
BLUE = "\033[94m"
GREEN = "\033[92m"
YELLOW = "\033[93m"
RED = "\033[91m"
BOLD = "\033[1m"
RESET = "\033[0m"

class AgentChatClient:
    def __init__(self):
        self.session_cookie = None
        self.user_id = None
        self.conversation_id = None
        self.websocket = None
        self.http_session = None
    
    async def login(self):
        """Login as an agent and get session cookie"""
        print(f"{YELLOW}Logging in as agent {AGENT_EMAIL}...{RESET}")
        
        self.http_session = aiohttp.ClientSession()
        login_data = {
            "username": AGENT_EMAIL,
            "password": AGENT_PASSWORD
        }
        
        async with self.http_session.post(
            f"{API_BASE_URL}/auth/login", 
            data=login_data
        ) as response:
            if response.status != 200:
                print(f"{RED}❌ Login failed: {await response.text()}{RESET}")
                return False
            
            # Extract session cookie
            cookies = response.cookies
            self.session_cookie = {cookie.key: cookie.value for cookie in cookies.values()}
            
            # Get user info
            async with self.http_session.get(f"{API_BASE_URL}/auth/me") as me_response:
                if me_response.status == 200:
                    user_data = await me_response.json()
                    self.user_id = user_data.get('id')
                    print(f"{GREEN}✅ Logged in successfully as {user_data.get('full_name')} (ID: {self.user_id}){RESET}")
                    return True
                else:
                    print(f"{RED}❌ Failed to get user info: {await me_response.text()}{RESET}")
                    return False
    
    async def list_conversations(self, include_archived=False):
        """List all conversations for the agent"""
        url = f"{API_BASE_URL}/conversations/"
        if include_archived:
            url += "?include_archived=true"
            
        async with self.http_session.get(url) as response:
            if response.status != 200:
                print(f"{RED}❌ Failed to list conversations: {await response.text()}{RESET}")
                return []
            
            data = await response.json()
            conversations = data.get('items', [])
            return conversations
    
    async def select_conversation(self):
        """Let the agent select a conversation to join"""
        conversations = await self.list_conversations()
        
        if not conversations:
            print(f"{YELLOW}No active conversations found.{RESET}")
            return None
        
        print(f"\n{BOLD}Available Conversations:{RESET}")
        for i, conv in enumerate(conversations):
            created_at = datetime.fromisoformat(conv['created_at'].replace('Z', '+00:00'))
            formatted_date = created_at.strftime("%Y-%m-%d %H:%M:%S")
            customer_id = conv.get('customer_id', 'Unknown')
            status = conv.get('status', 'Unknown')
            print(f"{i+1}. ID: {conv['id']} | Customer: {customer_id} | Status: {status} | Created: {formatted_date}")
        
        while True:
            try:
                choice = await aioconsole.ainput(f"\n{YELLOW}Select conversation number (or 'q' to quit): {RESET}")
                if choice.lower() in ['q', 'quit', 'exit']:
                    return None
                
                idx = int(choice) - 1
                if 0 <= idx < len(conversations):
                    self.conversation_id = conversations[idx]['id']
                    print(f"{GREEN}Selected conversation: {self.conversation_id}{RESET}")
                    return self.conversation_id
                else:
                    print(f"{RED}Invalid selection. Please try again.{RESET}")
            except ValueError:
                print(f"{RED}Please enter a valid number.{RESET}")
    
    async def connect_to_websocket(self):
        """Connect to the WebSocket for the selected conversation"""
        if not self.conversation_id:
            print(f"{RED}No conversation selected.{RESET}")
            return False
        
        uri = f"{WS_BASE_URL}/agent/{self.conversation_id}"
        
        # Create a websocket connection with cookies
        cookies_str = '; '.join([f"{k}={v}" for k, v in self.session_cookie.items()])
        
        try:
            self.websocket = await websockets.connect(
                uri, 
                extra_headers={"Cookie": cookies_str}
            )
            print(f"{GREEN}✅ Connected to WebSocket for conversation {self.conversation_id}{RESET}")
            return True
        except Exception as e:
            print(f"{RED}❌ Failed to connect to WebSocket: {e}{RESET}")
            return False
    
    async def run_chat_client(self):
        """Handle sending and receiving messages"""
        if not self.websocket:
            print(f"{RED}WebSocket not connected.{RESET}")
            return
        
        async def receive_messages():
            try:
                async for message in self.websocket:
                    data = json.loads(message)
                    sender = data.get('sender', 'system')
                    content = data.get('content', data.get('detail', 'No content'))
                    
                    # Blue for received messages
                    print(f"\n{BLUE}>> [{sender.upper()}]: {content}{RESET}")
                    print(">> Your reply: ", end="", flush=True)
            except websockets.ConnectionClosed:
                print(f"\n{YELLOW}Connection closed.{RESET}")
            except Exception as e:
                print(f"\n{RED}Error receiving message: {e}{RESET}")

        async def send_messages():
            while True:
                message_to_send = await aioconsole.ainput(">> Your reply: ")
                if message_to_send.lower() in ['quit', 'exit', 'q']:
                    break
                
                try:
                    await self.websocket.send(json.dumps({
                        "type": "text", 
                        "content": message_to_send
                    }))
                except Exception as e:
                    print(f"{RED}Error sending message: {e}{RESET}")
                    break
        
        receiver_task = asyncio.create_task(receive_messages())
        await send_messages()
        receiver_task.cancel()
    
    async def archive_conversation(self):
        """Archive the current conversation"""
        if not self.conversation_id:
            print(f"{RED}No conversation selected.{RESET}")
            return False
        
        url = f"{API_BASE_URL}/conversations/{self.conversation_id}/archive"
        async with self.http_session.post(url) as response:
            if response.status == 200:
                print(f"{GREEN}✅ Conversation archived successfully{RESET}")
                return True
            else:
                print(f"{RED}❌ Failed to archive conversation: {await response.text()}{RESET}")
                return False
    
    async def unarchive_conversation(self):
        """Unarchive the current conversation"""
        if not self.conversation_id:
            print(f"{RED}No conversation selected.{RESET}")
            return False
        
        url = f"{API_BASE_URL}/conversations/{self.conversation_id}/unarchive"
        async with self.http_session.post(url) as response:
            if response.status == 200:
                print(f"{GREEN}✅ Conversation unarchived successfully{RESET}")
                return True
            else:
                print(f"{RED}❌ Failed to unarchive conversation: {await response.text()}{RESET}")
                return False
    
    async def close(self):
        """Close all connections"""
        if self.websocket:
            await self.websocket.close()
        
        if self.http_session:
            await self.http_session.close()

async def main():
    client = AgentChatClient()
    
    if not await client.login():
        return
    
    print("\n" + "="*50)
    print(f"{BOLD}🚀 Yupcha Interactive Agent Chat Client{RESET}")
    print(f"👤 Agent: {AGENT_EMAIL}")
    print("--------------------------------------------------")
    print("Commands:")
    print("  'list' - List all conversations")
    print("  'archived' - List archived conversations")
    print("  'select' - Select a different conversation")
    print("  'archive' - Archive current conversation")
    print("  'unarchive' - Unarchive current conversation")
    print("  'quit' - Exit the client")
    print("="*50 + "\n")
    
    try:
        while True:
            command = await aioconsole.ainput(f"{YELLOW}Command (select/list/archived/quit): {RESET}")
            
            if command.lower() in ['quit', 'exit', 'q']:
                break
            
            elif command.lower() == 'list':
                conversations = await client.list_conversations()
                if conversations:
                    print(f"{GREEN}Found {len(conversations)} active conversations{RESET}")
            
            elif command.lower() == 'archived':
                conversations = await client.list_conversations(include_archived=True)
                if conversations:
                    print(f"{GREEN}Found {len(conversations)} conversations (including archived){RESET}")
            
            elif command.lower() == 'select':
                conversation_id = await client.select_conversation()
                if conversation_id and await client.connect_to_websocket():
                    print(f"{BOLD}Starting chat session. Type 'quit' to exit the chat.{RESET}")
                    await client.run_chat_client()
            
            elif command.lower() == 'archive':
                await client.archive_conversation()
            
            elif command.lower() == 'unarchive':
                await client.unarchive_conversation()
            
            else:
                print(f"{RED}Unknown command: {command}{RESET}")
    
    except Exception as e:
        print(f"{RED}❌ An error occurred: {e}{RESET}")
    
    finally:
        await client.close()
        print(f"\n{YELLOW}⏹️ Agent client finished.{RESET}")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print(f"\n{YELLOW}👋 Disconnecting...{RESET}")
