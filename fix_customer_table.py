#!/usr/bin/env python3
"""
Fix Customer Table Script
Adds the missing organization_id column to the customers table.
"""

import asyncio
import asyncpg
from app.core.config import settings

async def fix_customer_table():
    """Add missing organization_id column to customers table"""
    
    # Parse the database URL
    db_url = settings.DATABASE_URL.replace("postgresql+asyncpg://", "postgresql://")
    
    try:
        # Connect to the database
        conn = await asyncpg.connect(db_url)
        
        print("🔍 Checking customers table structure...")
        
        # Check if organization_id column exists
        result = await conn.fetch("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = 'customers' AND column_name = 'organization_id'
        """)
        
        if result:
            print("✅ organization_id column already exists!")
        else:
            print("➕ Adding organization_id column to customers table...")
            
            # Add the organization_id column
            await conn.execute("""
                ALTER TABLE customers 
                ADD COLUMN organization_id INTEGER REFERENCES organizations(id)
            """)
            
            print("✅ organization_id column added successfully!")
            
            # Set a default organization for existing customers
            print("🔧 Setting default organization for existing customers...")
            
            # Get the first organization ID
            org_result = await conn.fetch("SELECT id FROM organizations LIMIT 1")
            if org_result:
                default_org_id = org_result[0]['id']
                await conn.execute("""
                    UPDATE customers 
                    SET organization_id = $1 
                    WHERE organization_id IS NULL
                """, default_org_id)
                print(f"✅ Set default organization_id = {default_org_id} for existing customers")
            else:
                print("⚠️ No organizations found. Please create an organization first.")
        
        # Check current customers
        customers = await conn.fetch("SELECT id, customer_id, name, organization_id FROM customers LIMIT 5")
        print(f"\n📊 Current customers ({len(customers)}):")
        for customer in customers:
            print(f"  - ID: {customer['id']}, Customer ID: {customer['customer_id']}, Name: {customer['name']}, Org: {customer['organization_id']}")
        
        await conn.close()
        print("\n✅ Customer table fix completed!")
        
    except Exception as e:
        print(f"❌ Error fixing customer table: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(fix_customer_table())
