<template>
  <div id="app">
    <!-- Navigation Bar -->
    <nav class="navbar" v-if="isAuthenticated">
      <div class="nav-container">
        <div class="nav-brand">
          <i class="fas fa-robot"></i>
          <span>Yupcha AI</span>
        </div>

        <div class="nav-menu">
          <router-link to="/dashboard" class="nav-link">
            <i class="fas fa-tachometer-alt"></i> Dashboard
          </router-link>
          <router-link to="/conversations" class="nav-link">
            <i class="fas fa-comments"></i> Conversations
          </router-link>
          <router-link to="/customers" class="nav-link">
            <i class="fas fa-users"></i> Customers
          </router-link>
          <router-link to="/teams" class="nav-link" v-if="user?.role === 'admin'">
            <i class="fas fa-user-friends"></i> Teams
          </router-link>
          <router-link to="/organizations" class="nav-link" v-if="user?.role === 'admin'">
            <i class="fas fa-building"></i> Organizations
          </router-link>
          <router-link to="/users" class="nav-link" v-if="user?.role === 'admin'">
            <i class="fas fa-user-cog"></i> Users
          </router-link>
        </div>

        <div class="nav-user">
          <div class="user-info">
            <span class="user-name">{{ user?.full_name }}</span>
            <span class="user-role">{{ user?.role }}</span>
          </div>
          <button @click="logout" class="logout-btn">
            <i class="fas fa-sign-out-alt"></i> Logout
          </button>
        </div>
      </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content" :class="{ 'no-nav': !isAuthenticated }">
      <router-view></router-view>
    </main>

    <!-- Loading Overlay -->
    <div v-if="loading" class="loading-overlay">
      <div class="loading-spinner">
        <i class="fas fa-spinner fa-spin"></i>
        <p>Loading...</p>
      </div>
    </div>

    <!-- Toast Notifications -->
    <div class="toast-container">
      <div v-for="toast in toasts" :key="toast.id"
           :class="['toast', `toast-${toast.type}`]"
           @click="removeToast(toast.id)">
        <i :class="getToastIcon(toast.type)"></i>
        <span>{{ toast.message }}</span>
        <button class="toast-close">&times;</button>
      </div>
    </div>
  </div>
</template>

<script>
import { reactive, computed, onMounted, getCurrentInstance } from 'vue'
import { useRouter } from 'vue-router'
import axios from 'axios'

export default {
  name: 'App',
  setup() {
    const router = useRouter()

    // Global state
    const globalState = reactive({
      user: null,
      isAuthenticated: false,
      loading: false,
      toasts: []
    })

    const isAuthenticated = computed(() => globalState.isAuthenticated)
    const user = computed(() => globalState.user)
    const loading = computed(() => globalState.loading)
    const toasts = computed(() => globalState.toasts)

    // Toast system
    let toastId = 0
    const showToast = (message, type = 'info') => {
      const toast = {
        id: ++toastId,
        message,
        type
      }
      globalState.toasts.push(toast)

      setTimeout(() => {
        removeToast(toast.id)
      }, 5000)
    }

    const removeToast = (id) => {
      const index = globalState.toasts.findIndex(t => t.id === id)
      if (index > -1) {
        globalState.toasts.splice(index, 1)
      }
    }

    const getToastIcon = (type) => {
      const icons = {
        success: 'fas fa-check-circle',
        error: 'fas fa-exclamation-circle',
        warning: 'fas fa-exclamation-triangle',
        info: 'fas fa-info-circle'
      }
      return icons[type] || icons.info
    }

    // Auth methods
    const logout = async () => {
      try {
        globalState.loading = true
        await axios.post('/api/auth/logout')
        globalState.user = null
        globalState.isAuthenticated = false
        showToast('Logged out successfully', 'success')
        router.push('/login')
      } catch (error) {
        showToast('Logout failed', 'error')
      } finally {
        globalState.loading = false
      }
    }

    // Check authentication on app load
    onMounted(async () => {
      try {
        const response = await axios.get('/api/auth/me')
        globalState.user = response.data
        globalState.isAuthenticated = true
        if (router.currentRoute.value.path === '/login') {
          router.push('/dashboard')
        }
      } catch (error) {
        // User not authenticated
      }
    })

    // Provide global state to all components
    const app = getCurrentInstance()
    app.appContext.config.globalProperties.$globalState = globalState
    app.appContext.config.globalProperties.$showToast = showToast

    return {
      isAuthenticated,
      user,
      loading,
      toasts,
      logout,
      getToastIcon,
      removeToast
    }
  }
}
</script>
