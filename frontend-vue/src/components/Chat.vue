<template>
  <div class="chat-container">
    <div class="chat-header">
      <div class="chat-info">
        <button @click="$router.go(-1)" class="btn btn-sm btn-secondary">
          <i class="fas fa-arrow-left"></i>
          Back
        </button>
        <div class="conversation-details">
          <h2>Chat - Conversation {{ $route.params.id }}</h2>
          <p>Real-time chat interface</p>
        </div>
      </div>
    </div>

    <div class="chat-body">
      <div class="messages-container">
        <div class="empty-messages">
          <i class="fas fa-comments"></i>
          <p>Chat interface will be implemented here</p>
          <small>Real-time WebSocket communication</small>
        </div>
      </div>

      <div class="message-input">
        <form class="input-form">
          <div class="input-group">
            <input 
              type="text" 
              class="form-control" 
              placeholder="Type your message..."
              disabled
            >
            <button type="button" class="btn btn-secondary" disabled>
              <i class="fas fa-paperclip"></i>
            </button>
            <button type="submit" class="btn btn-primary" disabled>
              <i class="fas fa-paper-plane"></i>
              Send
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Chat'
}
</script>

<style scoped>
.chat-container {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 100px);
  background: white;
  border-radius: 8px;
  box-shadow: var(--shadow);
  overflow: hidden;
}

.chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid var(--border-color);
  background: var(--light-color);
}

.chat-info {
  display: flex;
  align-items: center;
  gap: 15px;
}

.conversation-details h2 {
  margin: 0 0 5px 0;
  font-size: 1.2rem;
  color: var(--dark-color);
}

.conversation-details p {
  margin: 0;
  color: var(--secondary-color);
  font-size: 0.9rem;
}

.chat-body {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: hidden;
}

.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
}

.empty-messages {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--secondary-color);
  text-align: center;
}

.empty-messages i {
  font-size: 3rem;
  margin-bottom: 15px;
}

.message-input {
  padding: 20px;
  border-top: 1px solid var(--border-color);
  background: white;
}

.input-form {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.input-group {
  display: flex;
  gap: 10px;
}

.input-group .form-control {
  flex: 1;
}

@media (max-width: 768px) {
  .chat-header {
    flex-direction: column;
    align-items: stretch;
    gap: 15px;
  }
  
  .chat-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
}
</style>
