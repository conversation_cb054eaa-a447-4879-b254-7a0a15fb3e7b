<template>
  <div class="teams">
    <div class="page-header">
      <div class="header-content">
        <h1><i class="fas fa-user-friends"></i> Teams</h1>
        <p>Manage support teams and agent assignments</p>
      </div>
      <div class="header-actions">
        <button @click="refreshTeams" class="btn btn-secondary">
          <i class="fas fa-sync-alt" :class="{ 'fa-spin': loading }"></i>
          Refresh
        </button>
        <button class="btn btn-primary">
          <i class="fas fa-plus"></i>
          Create Team
        </button>
      </div>
    </div>

    <div v-if="loading" class="loading-state">
      <i class="fas fa-spinner fa-spin"></i>
      <p>Loading teams...</p>
    </div>

    <div v-else-if="teams.length === 0" class="empty-state">
      <i class="fas fa-users-slash"></i>
      <p>No teams found</p>
    </div>

    <div v-else class="teams-grid">
      <div v-for="team in teams" :key="team.id" class="team-card">
        <div class="team-header">
          <div class="team-info">
            <h3>{{ team.name }}</h3>
            <p>{{ team.description }}</p>
          </div>
          <div class="team-actions">
            <button class="btn btn-sm btn-secondary">
              <i class="fas fa-edit"></i>
            </button>
            <button class="btn btn-sm btn-danger">
              <i class="fas fa-trash"></i>
            </button>
          </div>
        </div>

        <div class="team-stats">
          <div class="stat-item">
            <i class="fas fa-users"></i>
            <span>0 Members</span>
          </div>
          <div class="stat-item">
            <i class="fas fa-comments"></i>
            <span>0 Conversations</span>
          </div>
          <div class="stat-item">
            <i class="fas fa-building"></i>
            <span>{{ team.organization?.name || 'No Organization' }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, getCurrentInstance } from 'vue'
import axios from 'axios'

export default {
  name: 'Teams',
  setup() {
    const app = getCurrentInstance()
    const showToast = app.appContext.config.globalProperties.$showToast
    
    const loading = ref(false)
    const teams = ref([])
    
    const loadTeams = async () => {
      loading.value = true
      try {
        const response = await axios.get('/api/teams/')
        teams.value = response.data
      } catch (error) {
        console.error('Error loading teams:', error)
        showToast('Failed to load teams', 'error')
      } finally {
        loading.value = false
      }
    }
    
    const refreshTeams = () => {
      loadTeams()
    }
    
    onMounted(() => {
      loadTeams()
    })
    
    return {
      loading,
      teams,
      refreshTeams
    }
  }
}
</script>

<style scoped>
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 30px;
  gap: 20px;
}

.header-content h1 {
  color: var(--dark-color);
  margin-bottom: 5px;
}

.header-content p {
  color: var(--secondary-color);
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 10px;
  flex-shrink: 0;
}

.teams-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 20px;
}

.team-card {
  background: white;
  border-radius: 8px;
  box-shadow: var(--shadow);
  padding: 20px;
}

.team-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 15px;
}

.team-info h3 {
  margin: 0 0 5px 0;
  color: var(--dark-color);
}

.team-info p {
  margin: 0;
  color: var(--secondary-color);
  font-size: 0.9rem;
}

.team-actions {
  display: flex;
  gap: 5px;
}

.team-stats {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 0.9rem;
  color: var(--secondary-color);
}

.loading-state,
.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: var(--secondary-color);
}

.loading-state i,
.empty-state i {
  font-size: 2rem;
  margin-bottom: 10px;
  display: block;
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: stretch;
    gap: 15px;
  }
  
  .header-actions {
    justify-content: flex-end;
  }
  
  .teams-grid {
    grid-template-columns: 1fr;
  }
}
</style>
