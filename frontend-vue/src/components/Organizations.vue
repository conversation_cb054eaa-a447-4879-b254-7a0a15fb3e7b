<template>
  <div class="organizations">
    <div class="page-header">
      <div class="header-content">
        <h1><i class="fas fa-building"></i> Organizations</h1>
        <p>Manage organizations and their settings</p>
      </div>
      <div class="header-actions">
        <button @click="refreshOrganizations" class="btn btn-secondary">
          <i class="fas fa-sync-alt" :class="{ 'fa-spin': loading }"></i>
          Refresh
        </button>
        <button class="btn btn-primary">
          <i class="fas fa-plus"></i>
          Create Organization
        </button>
      </div>
    </div>

    <div v-if="loading" class="loading-state">
      <i class="fas fa-spinner fa-spin"></i>
      <p>Loading organizations...</p>
    </div>

    <div v-else-if="organizations.length === 0" class="empty-state">
      <i class="fas fa-building"></i>
      <p>No organizations found</p>
    </div>

    <div v-else class="organizations-grid">
      <div v-for="org in organizations" :key="org.id" class="org-card">
        <div class="org-header">
          <div class="org-info">
            <h3>{{ org.name }}</h3>
            <p>{{ org.description }}</p>
          </div>
          <div class="org-actions">
            <button class="btn btn-sm btn-secondary">
              <i class="fas fa-edit"></i>
            </button>
            <button class="btn btn-sm btn-danger">
              <i class="fas fa-trash"></i>
            </button>
          </div>
        </div>

        <div class="org-stats">
          <div class="stat-item">
            <i class="fas fa-user-friends"></i>
            <span>0 Teams</span>
          </div>
          <div class="stat-item">
            <i class="fas fa-comments"></i>
            <span>0 Conversations</span>
          </div>
          <div class="stat-item">
            <i class="fas fa-users"></i>
            <span>0 Users</span>
          </div>
        </div>

        <div class="org-details" v-if="org.website || org.contact_email">
          <div class="detail-row" v-if="org.website">
            <i class="fas fa-globe"></i>
            <a :href="org.website" target="_blank">{{ org.website }}</a>
          </div>
          <div class="detail-row" v-if="org.contact_email">
            <i class="fas fa-envelope"></i>
            <span>{{ org.contact_email }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, getCurrentInstance } from 'vue'
import axios from 'axios'

export default {
  name: 'Organizations',
  setup() {
    const app = getCurrentInstance()
    const showToast = app.appContext.config.globalProperties.$showToast
    
    const loading = ref(false)
    const organizations = ref([])
    
    const loadOrganizations = async () => {
      loading.value = true
      try {
        const response = await axios.get('/api/organizations/')
        organizations.value = response.data
      } catch (error) {
        console.error('Error loading organizations:', error)
        showToast('Failed to load organizations', 'error')
      } finally {
        loading.value = false
      }
    }
    
    const refreshOrganizations = () => {
      loadOrganizations()
    }
    
    onMounted(() => {
      loadOrganizations()
    })
    
    return {
      loading,
      organizations,
      refreshOrganizations
    }
  }
}
</script>

<style scoped>
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 30px;
  gap: 20px;
}

.header-content h1 {
  color: var(--dark-color);
  margin-bottom: 5px;
}

.header-content p {
  color: var(--secondary-color);
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 10px;
  flex-shrink: 0;
}

.organizations-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 20px;
}

.org-card {
  background: white;
  border-radius: 8px;
  box-shadow: var(--shadow);
  padding: 20px;
}

.org-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 15px;
}

.org-info h3 {
  margin: 0 0 5px 0;
  color: var(--dark-color);
}

.org-info p {
  margin: 0;
  color: var(--secondary-color);
  font-size: 0.9rem;
}

.org-actions {
  display: flex;
  gap: 5px;
}

.org-stats {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid var(--border-color);
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 0.9rem;
  color: var(--secondary-color);
}

.org-details {
  margin-bottom: 20px;
}

.detail-row {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  font-size: 0.9rem;
  color: var(--secondary-color);
}

.detail-row a {
  color: var(--primary-color);
  text-decoration: none;
}

.detail-row a:hover {
  text-decoration: underline;
}

.loading-state,
.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: var(--secondary-color);
}

.loading-state i,
.empty-state i {
  font-size: 2rem;
  margin-bottom: 10px;
  display: block;
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: stretch;
    gap: 15px;
  }
  
  .header-actions {
    justify-content: flex-end;
  }
  
  .organizations-grid {
    grid-template-columns: 1fr;
  }
}
</style>
