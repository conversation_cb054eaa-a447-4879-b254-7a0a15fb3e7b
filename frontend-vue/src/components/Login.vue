<template>
  <div class="login-container">
    <div class="login-card">
      <div class="login-header">
        <div class="login-logo">
          <i class="fas fa-robot"></i>
          <h1>Yupcha AI</h1>
        </div>
        <p class="login-subtitle">Customer Service Bot Administration</p>
      </div>

      <form @submit.prevent="handleLogin" class="login-form">
        <div class="form-group">
          <label for="email" class="form-label">
            <i class="fas fa-envelope"></i>
            Email Address
          </label>
          <input 
            type="email" 
            id="email"
            v-model="form.email" 
            class="form-control"
            placeholder="Enter your email"
            required
            :disabled="loading"
          >
        </div>

        <div class="form-group">
          <label for="password" class="form-label">
            <i class="fas fa-lock"></i>
            Password
          </label>
          <div class="password-input">
            <input 
              :type="showPassword ? 'text' : 'password'"
              id="password"
              v-model="form.password" 
              class="form-control"
              placeholder="Enter your password"
              required
              :disabled="loading"
            >
            <button 
              type="button" 
              class="password-toggle"
              @click="showPassword = !showPassword"
              :disabled="loading"
            >
              <i :class="showPassword ? 'fas fa-eye-slash' : 'fas fa-eye'"></i>
            </button>
          </div>
        </div>

        <div class="form-group">
          <button 
            type="submit" 
            class="btn btn-primary btn-login"
            :disabled="loading || !form.email || !form.password"
          >
            <i v-if="loading" class="fas fa-spinner fa-spin"></i>
            <i v-else class="fas fa-sign-in-alt"></i>
            {{ loading ? 'Signing In...' : 'Sign In' }}
          </button>
        </div>

        <div v-if="error" class="error-message">
          <i class="fas fa-exclamation-circle"></i>
          {{ error }}
        </div>
      </form>

      <div class="login-footer">
        <div class="demo-credentials">
          <h4><i class="fas fa-info-circle"></i> Demo Credentials</h4>
          <div class="credential-item">
            <strong>Admin:</strong>
            <button @click="fillCredentials('admin')" class="btn btn-sm btn-secondary">
              <EMAIL> / adminpassword
            </button>
          </div>
          <div class="credential-item">
            <strong>Agent:</strong>
            <button @click="fillCredentials('agent')" class="btn btn-sm btn-secondary">
              <EMAIL> / agentpassword
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { reactive, ref, getCurrentInstance } from 'vue'
import { useRouter } from 'vue-router'
import axios from 'axios'

export default {
  name: 'Login',
  setup() {
    const router = useRouter()
    
    const form = reactive({
      email: '',
      password: ''
    })
    
    const loading = ref(false)
    const error = ref('')
    const showPassword = ref(false)
    
    const fillCredentials = (type) => {
      if (type === 'admin') {
        form.email = '<EMAIL>'
        form.password = 'adminpassword'
      } else if (type === 'agent') {
        form.email = '<EMAIL>'
        form.password = 'agentpassword'
      }
    }
    
    const handleLogin = async () => {
      loading.value = true
      error.value = ''
      
      try {
        // Login request
        await axios.post('/api/auth/login', 
          `username=${encodeURIComponent(form.email)}&password=${encodeURIComponent(form.password)}`,
          {
            headers: {
              'Content-Type': 'application/x-www-form-urlencoded'
            }
          }
        )
        
        // Get user info
        const userResponse = await axios.get('/api/auth/me')
        const user = userResponse.data
        
        // Update global state
        const app = getCurrentInstance()
        const globalState = app.appContext.config.globalProperties.$globalState
        const showToast = app.appContext.config.globalProperties.$showToast
        
        globalState.user = user
        globalState.isAuthenticated = true
        
        showToast(`Welcome back, ${user.full_name}!`, 'success')
        router.push('/dashboard')
        
      } catch (err) {
        console.error('Login error:', err)
        if (err.response?.status === 401) {
          error.value = 'Invalid email or password'
        } else if (err.response?.status === 422) {
          error.value = 'Please check your email and password'
        } else {
          error.value = 'Login failed. Please try again.'
        }
      } finally {
        loading.value = false
      }
    }
    
    return {
      form,
      loading,
      error,
      showPassword,
      fillCredentials,
      handleLogin
    }
  }
}
</script>

<style scoped>
.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.login-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  padding: 40px;
  width: 100%;
  max-width: 450px;
  animation: slideUp 0.5s ease;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.login-header {
  text-align: center;
  margin-bottom: 30px;
}

.login-logo {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15px;
  margin-bottom: 10px;
}

.login-logo i {
  font-size: 2.5rem;
  color: var(--primary-color);
}

.login-logo h1 {
  font-size: 2rem;
  color: var(--dark-color);
  margin: 0;
}

.login-subtitle {
  color: var(--secondary-color);
  margin: 0;
  font-size: 0.9rem;
}

.login-form {
  margin-bottom: 30px;
}

.form-label {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  font-weight: 500;
  color: var(--dark-color);
}

.password-input {
  position: relative;
}

.password-toggle {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: var(--secondary-color);
  cursor: pointer;
  padding: 4px;
}

.password-toggle:hover {
  color: var(--primary-color);
}

.btn-login {
  width: 100%;
  padding: 12px;
  font-size: 1rem;
  font-weight: 600;
}

.error-message {
  background: #f8d7da;
  color: #721c24;
  padding: 12px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 15px;
  font-size: 0.9rem;
}

.login-footer {
  border-top: 1px solid var(--border-color);
  padding-top: 20px;
}

.demo-credentials h4 {
  color: var(--secondary-color);
  font-size: 0.9rem;
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.credential-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 10px;
  font-size: 0.85rem;
}

.credential-item strong {
  color: var(--dark-color);
}

.credential-item button {
  font-size: 0.75rem;
  padding: 4px 8px;
}

@media (max-width: 480px) {
  .login-card {
    padding: 30px 20px;
  }
  
  .login-logo h1 {
    font-size: 1.5rem;
  }
  
  .credential-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }
}
</style>
