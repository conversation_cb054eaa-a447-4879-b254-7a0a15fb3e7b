<template>
  <div class="customers">
    <div class="page-header">
      <div class="header-content">
        <h1><i class="fas fa-users"></i> Customers</h1>
        <p>Manage customer information and profiles</p>
      </div>
      <div class="header-actions">
        <button @click="refreshCustomers" class="btn btn-secondary">
          <i class="fas fa-sync-alt" :class="{ 'fa-spin': loading }"></i>
          Refresh
        </button>
        <button class="btn btn-primary">
          <i class="fas fa-user-plus"></i>
          Add Customer
        </button>
      </div>
    </div>

    <div class="card">
      <div class="card-header">
        <h3 class="card-title">
          Customers ({{ customers.length }})
        </h3>
      </div>

      <div v-if="loading" class="loading-state">
        <i class="fas fa-spinner fa-spin"></i>
        <p>Loading customers...</p>
      </div>

      <div v-else-if="customers.length === 0" class="empty-state">
        <i class="fas fa-user-slash"></i>
        <p>No customers found</p>
      </div>

      <div v-else class="table-responsive">
        <table class="table">
          <thead>
            <tr>
              <th>Customer</th>
              <th>Contact Info</th>
              <th>Location</th>
              <th>Created</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="customer in customers" :key="customer.id">
              <td>
                <div class="customer-info">
                  <div class="customer-avatar">
                    <i class="fas fa-user"></i>
                  </div>
                  <div>
                    <strong>{{ customer.name }}</strong>
                    <br>
                    <small class="text-muted">ID: {{ customer.customer_id }}</small>
                  </div>
                </div>
              </td>
              <td>
                <div>
                  <i class="fas fa-envelope"></i>
                  {{ customer.email || 'No email' }}
                </div>
                <div v-if="customer.phone">
                  <i class="fas fa-phone"></i>
                  {{ customer.phone }}
                </div>
              </td>
              <td>
                <div v-if="customer.location">
                  <i class="fas fa-map-marker-alt"></i>
                  {{ customer.location }}
                </div>
                <div v-if="customer.ip_address" class="text-muted">
                  <small>IP: {{ customer.ip_address }}</small>
                </div>
              </td>
              <td>
                {{ formatDate(customer.created_at) }}
              </td>
              <td>
                <div class="action-buttons">
                  <button class="btn btn-sm btn-secondary" title="Edit Customer">
                    <i class="fas fa-edit"></i>
                  </button>
                  <button class="btn btn-sm btn-primary" title="View Conversations">
                    <i class="fas fa-comments"></i>
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, getCurrentInstance } from 'vue'
import axios from 'axios'

export default {
  name: 'Customers',
  setup() {
    const app = getCurrentInstance()
    const showToast = app.appContext.config.globalProperties.$showToast
    
    const loading = ref(false)
    const customers = ref([])
    
    const loadCustomers = async () => {
      loading.value = true
      try {
        const response = await axios.get('/api/customers/')
        customers.value = response.data
      } catch (error) {
        console.error('Error loading customers:', error)
        showToast('Failed to load customers', 'error')
      } finally {
        loading.value = false
      }
    }
    
    const refreshCustomers = () => {
      loadCustomers()
    }
    
    const formatDate = (timestamp) => {
      return new Date(timestamp).toLocaleDateString()
    }
    
    onMounted(() => {
      loadCustomers()
    })
    
    return {
      loading,
      customers,
      refreshCustomers,
      formatDate
    }
  }
}
</script>

<style scoped>
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 30px;
  gap: 20px;
}

.header-content h1 {
  color: var(--dark-color);
  margin-bottom: 5px;
}

.header-content p {
  color: var(--secondary-color);
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 10px;
  flex-shrink: 0;
}

.table-responsive {
  overflow-x: auto;
}

.table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 20px;
}

.table th,
.table td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid var(--border-color);
}

.table th {
  background-color: var(--light-color);
  font-weight: 600;
  color: var(--dark-color);
}

.table tbody tr:hover {
  background-color: #f8f9fa;
}

.customer-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.customer-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: var(--primary-color);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.2rem;
}

.action-buttons {
  display: flex;
  gap: 5px;
}

.text-muted {
  color: var(--secondary-color);
}

.loading-state,
.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: var(--secondary-color);
}

.loading-state i,
.empty-state i {
  font-size: 2rem;
  margin-bottom: 10px;
  display: block;
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: stretch;
    gap: 15px;
  }
  
  .header-actions {
    justify-content: flex-end;
  }
  
  .table {
    font-size: 0.8rem;
  }
}
</style>
