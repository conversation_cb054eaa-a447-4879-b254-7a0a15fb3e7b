# Yupcha Customer Bot AI - Vue.js Frontend

A modern Vue.js 3 administration dashboard for the Yupcha Customer Service Bot AI system with npm-based development workflow.

## 🚀 Features

### **Complete Vue.js 3 Application**
- ✅ Modern Vue 3 Composition API
- ✅ Vue Router 4 for client-side routing
- ✅ Axios for HTTP requests
- ✅ Vite for fast development and building
- ✅ Hot module replacement (HMR)

### **Authentication & Authorization**
- ✅ Secure login system with session management
- ✅ Role-based access control (Admin/Agent)
- ✅ Auto-logout and session validation
- ✅ Demo credential quick-fill buttons

### **Dashboard & Management**
- ✅ Real-time statistics dashboard
- ✅ Conversation management
- ✅ Customer database management
- ✅ Team management (Admin only)
- ✅ Organization management (Admin only)
- ✅ User management (Admin only)

### **Modern Development Experience**
- ✅ `npm run dev` for development server
- ✅ Hot reload and fast refresh
- ✅ Vue DevTools integration
- ✅ Modern ES6+ JavaScript
- ✅ Component-based architecture

## 🛠️ Technology Stack

- **Vue.js 3** - Progressive JavaScript framework with Composition API
- **Vue Router 4** - Official router for Vue.js
- **Axios** - Promise-based HTTP client
- **Vite** - Next generation frontend tooling
- **CSS3** - Modern styling with CSS Grid and Flexbox

## 📁 Project Structure

```
frontend-vue/
├── public/
│   └── favicon.ico
├── src/
│   ├── assets/
│   │   ├── main.css          # Global styles and theme
│   │   └── logo.svg          # Vue logo
│   ├── components/
│   │   ├── Login.vue         # Authentication component
│   │   ├── Dashboard.vue     # Main dashboard
│   │   ├── Conversations.vue # Conversation management
│   │   ├── Customers.vue     # Customer management
│   │   ├── Teams.vue         # Team management (Admin)
│   │   ├── Organizations.vue # Organization management (Admin)
│   │   ├── Users.vue         # User management (Admin)
│   │   └── Chat.vue          # Real-time chat interface
│   ├── App.vue               # Root component with navigation
│   └── main.js               # Application entry point
├── index.html                # HTML template
├── package.json              # Dependencies and scripts
├── vite.config.js            # Vite configuration
└── README.md                 # This file
```

## 🚀 Getting Started

### **Prerequisites**
- Node.js 16+ and npm
- Yupcha Customer Bot AI backend running on `http://0.0.0.0:8000`

### **Installation & Development**

1. **Navigate to the Vue.js frontend directory:**
   ```bash
   cd frontend-vue
   ```

2. **Install dependencies:**
   ```bash
   npm install
   ```

3. **Start the development server:**
   ```bash
   npm run dev
   ```

4. **Open your browser:**
   Visit `http://localhost:5173`

### **Available Scripts**

```bash
# Start development server with hot reload
npm run dev

# Build for production
npm run build

# Preview production build
npm run preview
```

### **Demo Credentials**
The login page includes quick-fill buttons for demo credentials:

**Admin Access:**
- Email: `<EMAIL>`
- Password: `adminpassword`

**Agent Access:**
- Email: `<EMAIL>`
- Password: `agentpassword`

## 🎯 Development Workflow

### **Hot Reload Development**
1. Start the backend server:
   ```bash
   cd /path/to/yupcha-customerbot-ai
   uv run uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
   ```

2. Start the frontend development server:
   ```bash
   cd frontend-vue
   npm run dev
   ```

3. **Automatic reloading:** Changes to Vue components, CSS, or JavaScript will automatically reload in the browser.

### **Component Development**
- Each page is a separate Vue component in `src/components/`
- Global state management through reactive objects
- Consistent API patterns using Axios
- Reusable UI components and styles

### **API Integration**
- Base URL configured in `src/main.js`
- Axios interceptors for authentication
- Error handling with toast notifications
- Loading states and user feedback

## 🔧 Configuration

### **API Base URL**
To change the backend API URL, edit `src/main.js`:
```javascript
const API_BASE_URL = 'http://0.0.0.0:8000'  // Change this
axios.defaults.baseURL = API_BASE_URL
```

### **Development Server**
Vite configuration in `vite.config.js`:
```javascript
export default defineConfig({
  plugins: [vue()],
  server: {
    port: 5173,
    host: true  // Expose to network
  }
})
```

## 🎨 Styling & Theming

### **CSS Architecture**
- Global styles in `src/assets/main.css`
- CSS custom properties (variables) for theming
- Component-scoped styles in each `.vue` file
- Responsive design with mobile-first approach

### **Theme Customization**
Edit CSS variables in `src/assets/main.css`:
```css
:root {
  --primary-color: #0d6efd;    /* Change primary color */
  --secondary-color: #6c757d;  /* Change secondary color */
  /* ... other variables */
}
```

## 🚀 Production Build

### **Build for Production**
```bash
npm run build
```

This creates a `dist/` folder with optimized static files ready for deployment.

### **Preview Production Build**
```bash
npm run preview
```

### **Deployment Options**
The built files can be deployed to any static hosting service:

- **Netlify**: Drag and drop the `dist` folder
- **Vercel**: Connect your Git repository
- **GitHub Pages**: Upload the `dist` contents
- **AWS S3**: Static website hosting
- **Any web server**: Copy `dist` contents to web root

## 🔍 Development Tools

### **Vue DevTools**
- Automatically available in development
- Access at `http://localhost:5173/__devtools__/`
- Or press `Alt+Shift+D` in the app

### **Hot Module Replacement**
- Instant updates without page refresh
- Preserves component state during development
- Fast feedback loop for rapid development

### **Vite Features**
- Lightning-fast cold server start
- Instant hot module replacement
- Optimized build with Rollup
- Native ES modules in development

## 🐛 Troubleshooting

### **Common Issues**

**"Failed to load data" errors:**
- Ensure backend server is running on `http://0.0.0.0:8000`
- Check browser console for CORS errors
- Verify API endpoints are accessible

**Development server won't start:**
- Check if port 5173 is available
- Run `npm install` to ensure dependencies are installed
- Clear npm cache: `npm cache clean --force`

**Build errors:**
- Check for TypeScript/JavaScript syntax errors
- Ensure all imports are correct
- Run `npm run dev` first to catch development issues

## 📞 Support

For development issues:
1. Check the browser console for error messages
2. Verify the backend API is responding correctly
3. Review the Vue DevTools for component state
4. Check the terminal for build/server errors

## 🎉 Success!

You now have a complete npm-based Vue.js development environment!

### **Key Benefits:**
- ✅ **Fast Development**: Hot reload and instant feedback
- ✅ **Modern Tooling**: Vite, Vue 3, and latest JavaScript features
- ✅ **Production Ready**: Optimized builds and deployment-ready output
- ✅ **Developer Experience**: Vue DevTools, error handling, and debugging
- ✅ **Scalable Architecture**: Component-based and maintainable code

**Run `npm run dev` and start building amazing features!** 🚀
