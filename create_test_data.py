import asyncio
import random
from datetime import datetime, timedelta
from faker import Faker
from sqlalchemy import select
from uuid_extensions import uuid7  
from app.db.session import get_async_db
from app.models.user import User
from app.models.role import Role
from app.models.team import Team
from app.models.organization import Organization
from app.models.customer import Customer
from app.models.chat import Conversation, Message
from app.models.asset import Asset
from app.models.canned_response import CannedResponse
from app.models.associations import user_role_association
from app.core.security import get_password_hash
from sqlalchemy import insert

fake = Faker()

async def create_dummy_data():
    async for db in get_async_db():
        print("🏢 Creating organizations...")
        # Organizations
        orgs = [Organization(
            name=fake.company(),
            description=fake.catch_phrase(),
            website=fake.url(),
            email=fake.company_email(),
            phone=fake.phone_number(),
            address=fake.address(),
            is_active=True,
            is_deleted=False,
            created_at=fake.date_time_this_decade()
        ) for _ in range(10)]  # Reduced for testing
        db.add_all(orgs)
        await db.flush()

        print("👥 Creating teams...")
        # Teams
        teams = []
        # Ensure every organization has at least one team
        for org in orgs:
            teams.append(Team(
                name=f"{org.name} Default Team",
                description=f"Default team for {org.name}",
                organization_id=org.id,
                is_active=True,
                is_deleted=False,
                created_at=fake.date_time_this_decade()
            ))
        # Create additional random teams
        for _ in range(10): # Create 10 more random teams
            teams.append(Team(
                name=fake.bs().title(),
                description=fake.sentence(),
                organization_id=random.choice(orgs).id,
                is_active=True,
                is_deleted=False,
                created_at=fake.date_time_this_decade()
            ))
        db.add_all(teams)
        await db.flush()

        print("🔐 Creating roles...")
        # Roles - Create additional roles for each organization
        role_names = ["HR", "Support", "Supervisor", "Lead", "QA", "DevOps"]

        roles_to_create = []
        for org in orgs:
            for role_name in role_names:
                # Check if role already exists for this organization
                existing_role = await db.execute(
                    select(Role).where(Role.name == role_name, Role.company_id == org.id)
                )
                if not existing_role.scalar_one_or_none():
                    roles_to_create.append(Role(
                        name=role_name,
                        description=f"{role_name} role for {org.name}",
                        is_active=True,
                        is_system_role=False,
                        company_id=org.id,
                        created_at=fake.date_time_this_decade()
                    ))

        if roles_to_create:
            db.add_all(roles_to_create)
            await db.flush()

        # Get all roles (existing + newly created)
        all_roles_result = await db.execute(select(Role))
        roles = all_roles_result.scalars().all()

        print("👤 Creating users...")

        # Users
        users = []
        for _ in range(50):  # Reduced for testing
            org = random.choice(orgs)
            # Get teams for this organization
            org_teams = [t for t in teams if t.organization_id == org.id]
            team = random.choice(org_teams) if org_teams else None

            # Get roles for this organization (or system roles)
            org_roles = [r for r in roles if r.company_id == org.id or r.is_system_role]
            role = random.choice(org_roles) if org_roles else None

            user = User(
                full_name=fake.name(),
                email=fake.unique.email(),
                hashed_password=get_password_hash("password123"),  # Use proper hashing
                company_id=org.id,
                team_id=team.id if team else None,
                role_id=role.id if role else None,
                is_active=True,
                is_deleted=False,
                created_at=fake.date_time_this_decade()
            )
            users.append(user)

        db.add_all(users)
        await db.flush()

        print("🛒 Creating customers...")
        # Customers
        customers = [Customer(
            customer_id=str(uuid7()),
            name=fake.name(),
            email=fake.email(),
            phone=fake.phone_number(),
            ip_address=fake.ipv4(),
            location=fake.city(),
            user_agent=fake.user_agent(),
            customer_metadata=fake.text(max_nb_chars=50),
            is_active=True,
            is_deleted=False,
            organization_id=random.choice(orgs).id,
            created_at=fake.date_time_this_decade()
        ) for _ in range(30)]  # Reduced for testing
        db.add_all(customers)
        await db.flush()

        print("💬 Creating conversations...")
        # Conversations
        conversations = []
        for _ in range(20):  # Create some conversations
            customer = random.choice(customers)
            conversation = Conversation(
                customer_id=customer.id,
                organization_id=customer.organization_id,
                status=random.choice(['new', 'open', 'closed']),
                assigned_team_id=random.choice([t.id for t in teams if t.organization_id == customer.organization_id]),
                created_at=fake.date_time_this_decade()
            )
            conversations.append(conversation)

        db.add_all(conversations)
        await db.flush()

        print("📝 Creating canned responses...")
        # Canned Responses
        canned_responses = []
        for org in orgs:
            org_users = [u for u in users if u.company_id == org.id]
            if org_users:
                for _ in range(5):  # 5 canned responses per org
                    canned_response = CannedResponse(
                        title=fake.sentence(nb_words=4),
                        content=fake.text(max_nb_chars=200),
                        shortcut=fake.word()[:10],
                        category=random.choice(['greeting', 'support', 'closing', 'technical']),
                        organization_id=org.id,
                        created_by=random.choice(org_users).id,
                        is_active=True,
                        is_public=random.choice([True, False]),
                        usage_count=random.randint(0, 50),
                        created_at=fake.date_time_this_decade()
                    )
                    canned_responses.append(canned_response)

        db.add_all(canned_responses)
        await db.commit()
        print("✅ Test data created successfully!")

if __name__ == "__main__":
    asyncio.run(create_dummy_data())