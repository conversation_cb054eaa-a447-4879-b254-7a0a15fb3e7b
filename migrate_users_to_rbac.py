#!/usr/bin/env python3
"""
Data migration script to migrate existing users from old role/is_admin system to new RBAC system.
This should be run BEFORE applying the migration that drops the old columns.
"""

import asyncio
import sys
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, text

# Add the app directory to the path
sys.path.append('.')

from app.db.session import get_async_db
from app.models.user import User
from app.models.role import Role
from app.crud.crud_role import crud_role

async def migrate_users_to_rbac():
    """Migrate all existing users to the new RBAC system"""
    print("🚀 Starting user migration to RBAC system...")
    
    # Get database session
    db_gen = get_async_db()
    db = await db_gen.__anext__()
    try:
            # 1. Ensure required roles exist
            print("📋 Checking required roles...")
            
            # Check if Agent role exists
            agent_role = await crud_role.get_role_by_name(db, "Agent")
            if not agent_role:
                print("❌ Agent role not found! Please run the role creation migration first.")
                return False

            # Check if Admin role exists
            admin_role = await crud_role.get_role_by_name(db, "Admin")
            if not admin_role:
                print("❌ Admin role not found! Please run the role creation migration first.")
                return False
            
            print(f"✅ Found Agent role (ID: {agent_role.id})")
            print(f"✅ Found Admin role (ID: {admin_role.id})")
            
            # 2. Get all users with their current roles
            print("\n👥 Fetching all users...")
            result = await db.execute(
                select(User).where(User.is_deleted == False)
            )
            users = result.scalars().all()
            print(f"📊 Found {len(users)} users to migrate")
            
            # 3. Migrate each user
            migrated_count = 0
            for user in users:
                print(f"\n🔄 Migrating user: {user.email}")
                
                # Check if user already has roles assigned (skip if already migrated)
                if user.roles:
                    print(f"⏭️  User {user.email} already has roles: {[r.name for r in user.roles]}")
                    continue
                
                # Assign Agent role to ALL users
                try:
                    await crud_role.assign_role_to_user(db, user_id=user.id, role_id=agent_role.id)
                    print(f"✅ Assigned Agent role to {user.email}")

                    # If user was admin in old system, also assign Admin role
                    if hasattr(user, 'is_admin') and user.is_admin:
                        await crud_role.assign_role_to_user(db, user_id=user.id, role_id=admin_role.id)
                        print(f"✅ Assigned Admin role to {user.email} (was admin)")
                    
                    migrated_count += 1
                    
                except Exception as e:
                    print(f"❌ Error migrating user {user.email}: {e}")
                    continue
            
            # 4. Verify migration
            print(f"\n🔍 Verifying migration...")
            result = await db.execute(
                select(User).where(User.is_deleted == False)
            )
            users = result.scalars().all()
            
            users_with_roles = 0
            admin_users = 0
            agent_users = 0
            
            for user in users:
                if user.roles:
                    users_with_roles += 1
                    role_names = [r.name for r in user.roles]
                    if "Admin" in role_names:
                        admin_users += 1
                    if "Agent" in role_names:
                        agent_users += 1
            
            print(f"📊 Migration Results:")
            print(f"   - Total users: {len(users)}")
            print(f"   - Users with roles: {users_with_roles}")
            print(f"   - Users with Agent role: {agent_users}")
            print(f"   - Users with Admin role: {admin_users}")
            print(f"   - Successfully migrated: {migrated_count}")
            
            if users_with_roles == len(users):
                print("✅ All users successfully migrated to RBAC system!")
                return True
            else:
                print("⚠️  Some users may not have been migrated properly.")
                return False

    except Exception as e:
        print(f"❌ Migration failed: {e}")
        return False
    finally:
        await db.close()

if __name__ == "__main__":
    success = asyncio.run(migrate_users_to_rbac())
    if success:
        print("\n🎉 Migration completed successfully!")
        print("💡 You can now run: alembic upgrade head")
        sys.exit(0)
    else:
        print("\n💥 Migration failed!")
        sys.exit(1)
