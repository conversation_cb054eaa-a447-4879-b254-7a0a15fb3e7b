import json
import logging
from fastapi import Depends, BackgroundTasks
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Optional

from app.db.session import get_async_db
from app.crud import crud_chat
from app.schemas.chat import ConversationCreate
from app.models.chat import Conversation
from app.api.endpoints.notifications import send_new_conversation_notification

logger = logging.getLogger(__name__)

def send_team_notification_email(team_email: str, conversation_id: int):
    """Send notification email to team about new conversation"""
    logger.info(f"📧 Adding background task to email {team_email} about new conversation {conversation_id}")
    # In a real implementation, you would send an actual email here

class ConversationService:
    def __init__(self, db: AsyncSession, background_tasks: BackgroundTasks = None):
        self.db = db
        self.background_tasks = background_tasks

    async def create_conversation(self, conversation_data: ConversationCreate) -> Conversation:
        """
        Creates a new conversation, auto-assigns it, and triggers all necessary notifications.
        This is the single entry point for creating conversations.
        """
        try:
            # 1. Create the conversation in the database
            # The CRUD operation already handles auto-assignment to the default team
            new_conversation = await crud_chat.create_conversation(db=self.db, conversation=conversation_data)
            
            # Eagerly load relationships needed for notifications
            await self.db.refresh(new_conversation, ['assigned_team', 'customer', 'organization'])

            # 2. Trigger real-time WebSocket notification to all agents in the organization
            await send_new_conversation_notification(
                conversation_id=new_conversation.id,
                customer_id=new_conversation.customer.id,
                customer_name=new_conversation.customer.name or f"Customer #{new_conversation.customer.id}",
                organization_id=new_conversation.organization_id,
                team_id=new_conversation.assigned_team_id
            )

            # 3. Trigger background email notification if a team was assigned
            if new_conversation.assigned_team_id:
                team_email = "<EMAIL>"  # In a real app, you'd get this from team.email
                self.background_tasks.add_task(
                    send_team_notification_email, team_email, new_conversation.id
                )

            logger.info(f"✅ Successfully created conversation {new_conversation.id} with notifications")
            return new_conversation
            
        except Exception as e:
            logger.error(f"❌ Error creating conversation: {e}")
            raise
