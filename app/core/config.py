from typing import List, Optional, Any
from pydantic import BaseModel, Field, field_validator, PostgresDsn
from pydantic_settings import BaseSettings, SettingsConfigDict
from dotenv import load_dotenv

load_dotenv()

class S3Settings(BaseModel):
    ENDPOINT_URL: str
    ACCESS_KEY: str
    SECRET_KEY: str
    BUCKET_NAME: str
    REGION: str

class Settings(BaseSettings):
    """Main application settings, loaded from environment variables."""
    
    APP_NAME: str = "Yupcha Customer Service"
    DEBUG: bool = False
    LOG_LEVEL: str = "info"
    SECRET_KEY: str 
    SESSION_COOKIE_NAME: str

    DB_USER: str
    DB_PASSWORD: str
    DB_HOST: str
    DB_PORT: int
    DB_NAME: str
    
    DATABASE_URL: Optional[PostgresDsn] = None
    ASYNC_DATABASE_URL: Optional[PostgresDsn] = None

    @field_validator("DATABASE_URL", "ASYNC_DATABASE_URL", mode='before')
    @classmethod
    def assemble_db_urls(cls, v: Optional[str], info: Any) -> Any:
        if isinstance(v, str):
            return v
            
        scheme_map = {
            "DATABASE_URL": "postgresql",
            "ASYNC_DATABASE_URL": "postgresql+asyncpg"
        }
        
        return PostgresDsn.build(
            scheme=scheme_map[info.field_name],
            username=info.data.get("DB_USER"),
            password=info.data.get("DB_PASSWORD"),
            host=info.data.get("DB_HOST"),
            port=info.data.get("DB_PORT"),
            path=info.data.get('DB_NAME') or "",
        )
        
    HOST: str = "0.0.0.0"
    PORT: int = 8000
    
    ALLOWED_ORIGINS: Optional[List[str]] = None

    @field_validator("ALLOWED_ORIGINS", mode="before")
    @classmethod
    def assemble_cors_origins(cls, v: Any) -> List[str]:
        if isinstance(v, str) and v:
            return [i.strip() for i in v.split(",")]
        if v is None:
            return []
        return v

    REDIS_URL: str

    S3: S3Settings

    model_config = SettingsConfigDict(
        env_file=".env",
        env_prefix="YUPCHA_",
        env_nested_delimiter='__',
        case_sensitive=False,
        extra="ignore"
    )

settings = Settings()