import asyncio
import json
import logging
from fastapi import WebSocket
from redis import asyncio as aioredis

from app.core.broadcaster import broadcaster
from app.core.config import settings

logger = logging.getLogger(__name__)

# A simple Redis client for presence tracking (separate from broadcaster's)
redis_client = aioredis.from_url(settings.REDIS_URL, encoding="utf-8", decode_responses=True)

class WebSocketManager:
    """
    Manages WebSocket connections and broadcasts messages using a Redis backend.
    This architecture is horizontally scalable.
    """

    def get_chat_channel(self, conversation_id: int) -> str:
        """Returns the Redis channel name for a given conversation."""
        return f"chat-conversation-{conversation_id}"

    async def connect(self, websocket: WebSocket, channel: str):
        """
        Subscribes a WebSocket to a specific channel and forwards messages.
        This function runs as a task for each connected client.
        """
        async with broadcaster.subscribe(channel=channel) as subscriber:
            try:
                # First, send a confirmation to the client
                await websocket.send_text(json.dumps({"type": "system", "detail": "Connection successful."}))

                # Then, listen for messages from the channel and send to the client
                async for event in subscriber:
                    await websocket.send_text(event.message)
            except Exception as e:
                logger.error(f"Error in WebSocket listener for channel {channel}: {e}")
            finally:
                logger.info(f"Listener for channel {channel} stopped.")

    async def broadcast(self, channel: str, message: str):
        """
        Broadcasts a message to a specific Redis channel.
        """
        await broadcaster.publish(channel=channel, message=message)
        logger.debug(f"Broadcasted to channel {channel}")

    # --- Agent Presence Tracking using Redis ---

    def get_presence_key(self, conversation_id: int) -> str:
        """Returns the Redis key for tracking agent presence in a conversation."""
        return f"presence-conversation-{conversation_id}"

    async def set_agent_active(self, conversation_id: int):
        """
        Marks an agent as active in a conversation using a Redis key with a timeout.
        We use INCR to handle multiple agents and EXPIRE to auto-clean.
        """
        key = self.get_presence_key(conversation_id)
        # Increment the count of active agents for this conversation
        await redis_client.incr(key)
        # Set an expiry of 60 seconds. The agent client should send pings
        # to keep this alive, or the agent's connection handler should refresh it.
        await redis_client.expire(key, 60)
        logger.info(f"Agent presence marked for conversation {conversation_id}")

    async def set_agent_inactive(self, conversation_id: int):
        """Marks an agent as inactive in a conversation."""
        key = self.get_presence_key(conversation_id)
        # Decrement the agent count. If it's the last one, the key will be 0.
        # It will eventually expire if not incremented again.
        await redis_client.decr(key)
        logger.info(f"Agent presence removed for conversation {conversation_id}")

    async def is_agent_active(self, conversation_id: int) -> bool:
        """
        Checks if any agent is marked as active in the conversation from Redis.
        Returns True if the count of agents is greater than 0.
        """
        key = self.get_presence_key(conversation_id)
        agent_count = await redis_client.get(key)
        return agent_count is not None and int(agent_count) > 0

    # --- Organization Notification Channel Methods (using Redis) ---

    def get_org_channel(self, organization_id: int) -> str:
        """Returns the Redis channel name for organization notifications."""
        return f"org-notifications-{organization_id}"

    async def connect_to_org_channel(self, websocket: WebSocket, organization_id: int, user_id: int, role: str):
        """
        Connects an agent/admin to their organization's notification channel.
        """
        channel = self.get_org_channel(organization_id)

        # Start listening to the organization channel
        listen_task = asyncio.create_task(self.connect(websocket, channel))

        logger.info(f"User {user_id} ({role}) connected to notification channel for organization {organization_id}")

        # Send welcome message
        welcome_message = {
            "type": "notification_channel_connected",
            "organization_id": organization_id,
            "user_id": user_id,
            "message": "Connected to organization notification channel"
        }
        await websocket.send_text(json.dumps(welcome_message))

        return listen_task

    async def broadcast_to_organization(self, message: str, organization_id: int):
        """Broadcasts a message to all users in an organization's notification channel."""
        channel = self.get_org_channel(organization_id)
        await self.broadcast(channel, message)
        logger.debug(f"Broadcasted to organization {organization_id}")

    def disconnect_from_org_channel(self, websocket: WebSocket, organization_id: int = None):
        """Disconnects a user from the notification channel."""
        # With Redis pub/sub, disconnection is handled automatically when WebSocket closes
        logger.info(f"User disconnected from notification channel for organization {organization_id}")

    # --- Backward compatibility methods for existing code ---

    async def broadcast_to_conversation(self, message: str, conversation_id: int):
        """Broadcast a message to all clients in a conversation (Redis-based)"""
        channel = self.get_chat_channel(conversation_id)
        await self.broadcast(channel, message)

    async def is_agent_in_conversation(self, conversation_id: int) -> bool:
        """Checks if any agent or admin is currently connected to a conversation."""
        return await self.is_agent_active(conversation_id)

# A single, global instance of the manager
manager = WebSocketManager()
