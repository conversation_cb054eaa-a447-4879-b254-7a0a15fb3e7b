from fastapi import WebSocket
from typing import Dict, List, Set
import json
import logging

logger = logging.getLogger(__name__)

class WebSocketManager:
    def __init__(self):
        # Store active connections per conversation
        # conversation_id -> List[WebSocket]
        self.active_connections: Dict[int, List[WebSocket]] = {}

        # Track connection metadata (user type, etc.)
        # websocket -> {"conversation_id": int, "user_type": str, "user_id": str}
        self.connection_metadata: Dict[WebSocket, Dict] = {}

        # NEW: Store organization-wide notification channels
        # organization_id -> List[{"ws": WebSocket, "user_id": int, "role": str}]
        self.organization_channels: Dict[int, List[Dict]] = {}

        # Track organization channel metadata
        # websocket -> {"organization_id": int, "user_id": int, "role": str}
        self.org_channel_metadata: Dict[WebSocket, Dict] = {}

    async def connect(self, websocket: WebSocket, conversation_id: int, user_type: str = "user", user_id: str = None):
        """Connect a client to a conversation room"""
        #! await websocket.accept()

        # Add to conversation room
        if conversation_id not in self.active_connections:
            self.active_connections[conversation_id] = []
        self.active_connections[conversation_id].append(websocket)

        # Store metadata
        self.connection_metadata[websocket] = {
            "conversation_id": conversation_id,
            "user_type": user_type,
            "user_id": user_id or f"{user_type}_{id(websocket)}"
        }

        logger.info(f"{user_type} connected to conversation {conversation_id}")

        #* Notify others in the room about new connection
        await self.broadcast_to_conversation_except(
            json.dumps({
                "type": "user_joined",
                "user_type": user_type,
                "user_id": user_id,
                "conversation_id": conversation_id
            }),
            conversation_id,
            exclude_websocket=websocket
        )

    def disconnect(self, websocket: WebSocket, conversation_id: int = None):
        """Disconnect a client from a conversation"""
        # Get conversation_id from metadata if not provided
        if conversation_id is None and websocket in self.connection_metadata:
            conversation_id = self.connection_metadata[websocket]["conversation_id"]

        if conversation_id and conversation_id in self.active_connections:
            if websocket in self.active_connections[conversation_id]:
                self.active_connections[conversation_id].remove(websocket)

                # Clean up empty conversation rooms
                if not self.active_connections[conversation_id]:
                    del self.active_connections[conversation_id]

        # Get user info before removing metadata
        user_info = self.connection_metadata.get(websocket, {})
        user_type = user_info.get("user_type", "unknown")
        user_id = user_info.get("user_id", "unknown")

        # Remove metadata
        if websocket in self.connection_metadata:
            del self.connection_metadata[websocket]

        logger.info(f"{user_type} disconnected from conversation {conversation_id}")

        # Notify others about disconnection (fire and forget)
        if conversation_id:
            try:
                import asyncio
                asyncio.create_task(self.broadcast_to_conversation(
                    json.dumps({
                        "type": "user_left",
                        "user_type": user_type,
                        "user_id": user_id,
                        "conversation_id": conversation_id
                    }),
                    conversation_id
                ))
            except Exception as e:
                logger.error(f"Error notifying about disconnection: {e}")

    async def send_personal_message(self, message: str, websocket: WebSocket):
        """Send a message to a specific websocket"""
        try:
            await websocket.send_text(message)
        except Exception as e:
            logger.error(f"Error sending personal message: {e}")
            # Remove broken connection
            self.disconnect(websocket)

    async def broadcast_to_conversation(self, message: str, conversation_id: int):
        """Broadcast a message to all clients in a conversation"""
        if conversation_id not in self.active_connections:
            return

        # Create a copy of the list to avoid modification during iteration
        connections = self.active_connections[conversation_id].copy()

        for connection in connections:
            try:
                await connection.send_text(message)
            except Exception as e:
                logger.error(f"Error broadcasting to connection: {e}")
                # Remove broken connection
                self.disconnect(connection, conversation_id)

    async def broadcast_to_conversation_except(self, message: str, conversation_id: int, exclude_websocket: WebSocket):
        """Broadcast a message to all clients in a conversation except one"""
        if conversation_id not in self.active_connections:
            return

        connections = self.active_connections[conversation_id].copy()

        for connection in connections:
            if connection != exclude_websocket:
                try:
                    await connection.send_text(message)
                except Exception as e:
                    logger.error(f"Error broadcasting to connection: {e}")
                    self.disconnect(connection, conversation_id)

    def get_conversation_participants(self, conversation_id: int) -> List[Dict]:
        """Get list of participants in a conversation"""
        if conversation_id not in self.active_connections:
            return []

        participants = []
        for websocket in self.active_connections[conversation_id]:
            if websocket in self.connection_metadata:
                metadata = self.connection_metadata[websocket]
                participants.append({
                    "user_type": metadata.get("user_type"),
                    "user_id": metadata.get("user_id")
                })

        return participants

    def get_active_conversations(self) -> List[int]:
        """Get list of conversation IDs with active connections"""
        return list(self.active_connections.keys())

    def get_connection_count(self, conversation_id: int) -> int:
        """Get number of active connections for a conversation"""
        return len(self.active_connections.get(conversation_id, []))

    async def is_agent_in_conversation(self, conversation_id: int) -> bool:
        """Checks if any agent or admin is currently connected to a conversation."""
        if conversation_id in self.active_connections:
            for conn_info in self.active_connections[conversation_id]:
                if conn_info["type"] in ["agent", "admin"]:
                    return True
        return False
    
    # --- NEW: Organization Notification Channel Methods ---

    async def connect_to_org_channel(self, websocket: WebSocket, organization_id: int, user_id: int, role: str):
        """Connects an agent/admin to their organization's notification channel."""
        await websocket.accept()

        if organization_id not in self.organization_channels:
            self.organization_channels[organization_id] = []

        connection_info = {
            "ws": websocket,
            "user_id": user_id,
            "role": role
        }
        self.organization_channels[organization_id].append(connection_info)

        # Store metadata for this connection
        self.org_channel_metadata[websocket] = {
            "organization_id": organization_id,
            "user_id": user_id,
            "role": role
        }

        logger.info(f"User {user_id} ({role}) connected to notification channel for organization {organization_id}")

    def disconnect_from_org_channel(self, websocket: WebSocket, organization_id: int = None):
        """Disconnects a user from the notification channel."""
        # Get organization_id from metadata if not provided
        if organization_id is None and websocket in self.org_channel_metadata:
            organization_id = self.org_channel_metadata[websocket]["organization_id"]

        if organization_id and organization_id in self.organization_channels:
            # Find and remove the connection
            self.organization_channels[organization_id] = [
                conn for conn in self.organization_channels[organization_id]
                if conn["ws"] != websocket
            ]

            # Clean up empty organization channels
            if not self.organization_channels[organization_id]:
                del self.organization_channels[organization_id]

        # Remove metadata
        if websocket in self.org_channel_metadata:
            user_info = self.org_channel_metadata[websocket]
            del self.org_channel_metadata[websocket]
            logger.info(f"User {user_info.get('user_id')} disconnected from notification channel for organization {organization_id}")

    async def broadcast_to_organization(self, message: str, organization_id: int):
        """Broadcasts a message to all users in an organization's notification channel."""
        if organization_id not in self.organization_channels:
            logger.debug(f"No active notification channels for organization {organization_id}")
            return

        # Use a copy to prevent issues if a connection drops during broadcast
        connections = self.organization_channels[organization_id].copy()

        for connection_info in connections:
            try:
                await connection_info["ws"].send_text(message)
            except Exception as e:
                logger.error(f"Error broadcasting to organization channel: {e}")
                # If sending fails, disconnect them from the channel
                self.disconnect_from_org_channel(connection_info["ws"], organization_id)

    def get_org_channel_participants(self, organization_id: int) -> List[Dict]:
        """Get list of participants in an organization's notification channel"""
        if organization_id not in self.organization_channels:
            return []

        return [
            {
                "user_id": conn["user_id"],
                "role": conn["role"]
            }
            for conn in self.organization_channels[organization_id]
        ]

    def get_active_organizations(self) -> List[int]:
        """Get list of organization IDs with active notification channels"""
        return list(self.organization_channels.keys())

# Create a single global instance
manager = WebSocketManager()
