from fastapi import API<PERSON><PERSON><PERSON>, Depends, HTTPException, BackgroundTasks
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List
import json

from app.db.session import get_async_db
from app.crud import crud_chat, crud_user, crud_customer, crud_team
from app.schemas.chat import ConversationCreate, ConversationResponse, MessageResponse
from app.schemas.customer import CustomerCreate
from app.models.user import User, UserRole
from app.auth.dependencies import get_current_active_admin, get_current_active_agent
from app.core.websocket_manager import manager

router = APIRouter()

#* A placeholder function for sending an email
def send_team_notification_email(team_email: str, conversation_id: int):
    """Send notification email to team about new conversation"""
    print(f"📧 Sending email to {team_email} about new conversation {conversation_id}")

@router.post("/", response_model=ConversationResponse)
async def create_conversation(
    conversation: ConversationCreate,
    background_tasks: BackgroundTasks,
    db: AsyncSession = Depends(get_async_db)
):
    """Create a new conversation and trigger a background notification."""
    new_conversation = await crud_chat.create_conversation(db=db, conversation=conversation)

    #* NEW: Broadcast to the organization's notification channel
    #* This ensures all online agents for that org get a real-time alert.
    notification_payload = {
        "type": "new_conversation",
        "data": {
            "id": new_conversation.id,
            "customer_id": new_conversation.customer_id,
            "status": new_conversation.status.value,
            "organization_id": new_conversation.organization_id,
            "assigned_team_id": new_conversation.assigned_team_id,
            "created_at": new_conversation.created_at.isoformat(),
        }
    }
    await manager.broadcast_to_organization(
        json.dumps(notification_payload),
        new_conversation.organization_id
    )

    # If the conversation was assigned to a team, send a notification
    if new_conversation.assigned_team_id:
        await db.refresh(new_conversation, ['assigned_team'])  # Eagerly load the team
        team_email = "<EMAIL>"  # Fetch the team's email in a real app
        background_tasks.add_task(
            send_team_notification_email, team_email, new_conversation.id
        )

    return new_conversation

@router.get("/{conversation_id}", response_model=ConversationResponse)
async def get_conversation(
    conversation_id: int,
    db: AsyncSession = Depends(get_async_db)
):
    """Get a specific conversation by ID"""
    conversation = await crud_chat.get_conversation(db=db, conversation_id=conversation_id)
    if conversation is None:
        raise HTTPException(status_code=404, detail="Conversation not found")
    return conversation

@router.get("/", response_model=List[ConversationResponse])
async def get_conversations(
    skip: int = 0,
    limit: int = 100,
    db: AsyncSession = Depends(get_async_db),
    current_admin: User = Depends(get_current_active_admin)
):
    """Get all conversations with pagination. Admin access required."""
    return await crud_chat.get_conversations(db=db, skip=skip, limit=limit)

@router.get("/{conversation_id}/messages", response_model=List[MessageResponse])
async def get_conversation_messages(
    conversation_id: int,
    db: AsyncSession = Depends(get_async_db)
):
    """Get all messages for a specific conversation"""
    messages = await crud_chat.get_messages_by_conversation(db=db, conversation_id=conversation_id)
    return messages

@router.post("/{conversation_id}/assign/{team_id}", response_model=ConversationResponse)
async def assign_team(
    conversation_id: int,
    team_id: int,
    db: AsyncSession = Depends(get_async_db),
    current_admin: User = Depends(get_current_active_admin)
):
    """Assign a team to a conversation. Admin access required."""
    team = await crud_team.get_team(db=db, team_id=team_id)
    if not team:
        raise HTTPException(status_code=404, detail="Team not found")

    conversation = await crud_chat.assign_team_to_conversation(
        db, conversation_id=conversation_id, team_id=team_id
    )
    if not conversation:
        raise HTTPException(status_code=404, detail="Conversation not found")
    return conversation

@router.get("/unassigned", response_model=List[ConversationResponse])
async def get_unassigned_conversations(
    db: AsyncSession = Depends(get_async_db),
    current_agent: User = Depends(get_current_active_agent)
):
    """Get unassigned conversations. Agent or Admin access required."""
    return await crud_chat.get_unassigned_conversations(db=db)

@router.get("/team/{team_id}", response_model=List[ConversationResponse])
async def get_team_conversations(
    team_id: int,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_active_agent)
):
    """Get conversations for a specific team. Admins can see any, agents only their team."""
    if current_user.role == UserRole.agent and current_user.team_id != team_id:
        raise HTTPException(status_code=403, detail="Agents can only view their team's conversations")

    return await crud_chat.get_conversations_by_team(db=db, team_id=team_id)
