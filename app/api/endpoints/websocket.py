from fastapi import API<PERSON><PERSON><PERSON>, WebSocket, WebSocketDisconnect, Query, <PERSON>ie, status
from sqlalchemy.ext.asyncio import AsyncSession
import json
import asyncio
from typing import Optional

from app.core.websocket_manager import WebSocket<PERSON>anager, manager
from app.db.session import Async<PERSON>ess<PERSON><PERSON>ocal
from app.crud import crud_chat, crud_user, crud_customer
from app.schemas.chat import MessageCreate
from app.schemas.customer import CustomerCreate
from app.models.user import User, UserRole
from app.models.customer import Customer
from app.auth.dependencies import serializer, get_current_user

router = APIRouter()
manager = WebSocketManager()

# Import notification function (will be available after notifications are added to API)
async def send_new_message_notification_if_available(
    message_id: int,
    conversation_id: int,
    customer_id: int,
    customer_name: str,
    message_content: str,
    organization_id: int,
    team_id: Optional[int] = None
):
    """Send notification if notification service is available"""
    try:
        from app.api.endpoints.notifications import send_new_message_notification
        await send_new_message_notification(
            message_id=message_id,
            conversation_id=conversation_id,
            customer_id=customer_id,
            customer_name=customer_name,
            message_content=message_content,
            organization_id=organization_id,
            team_id=team_id
        )
    except ImportError:
        # Notification service not available yet
        pass
    except Exception as e:
        print(f"Error sending notification: {e}")

async def check_active_agents_in_conversation(conversation_id: int) -> bool:
    """Check if any agents or admins are currently connected to this conversation"""
    connections = manager.get_conversation_participants(conversation_id)
    for participant in connections:
        if participant.get("user_type") in ["agent", "admin"]:
            return True
    return False

async def get_authenticated_user_from_cookie(
    session_cookie: Optional[str], db: AsyncSession
) -> Optional[User]:
    """Helper to safely authenticate a user from a session cookie for WebSockets."""
    if not session_cookie:
        return None
    try:
        user_id_str = serializer.loads(session_cookie, max_age=86400)
        user_id = int(user_id_str)
        return await crud_user.get_user(db, user_id=user_id)
    except Exception:
        return None

@router.websocket("/chat/{conversation_id}")
async def websocket_chat(
    websocket: WebSocket,
    conversation_id: int,
    customer_id: Optional[str] = Query(None), # For customers
    token: Optional[str] = Query(None), # For agents/admins via token
    yupcha_session: Optional[str] = Cookie(None), # For agents/admins via cookie
):
    """
    Unified WebSocket endpoint for real-time chat.
    - Authenticated agents/admins connect via session cookie.
    - Customers connect using a `customer_id`. If the customer doesn't exist, they are created.
    """
    db: AsyncSession = AsyncSessionLocal()
    user: Optional[User] = None
    customer: Optional[Customer] = None
    conn_type: str = ""
    conn_id: str = ""

    await websocket.accept()

    #* Determine connection type: Agent/Admin or Customer
    if yupcha_session:
        user = await get_authenticated_user_from_cookie(yupcha_session, db)
        if user:
            conn_type = user.role.value
            conn_id = str(user.id)
    elif token:
        # Try to authenticate via token
        from app.auth.dependencies import get_current_user_from_websocket_token
        user = await get_current_user_from_websocket_token(token, db)
        if user:
            conn_type = user.role.value
            conn_id = str(user.id)
    
    #* This block now handles both existing and NEW customers
    elif customer_id:
        customer = await crud_customer.get_or_create_customer(db, customer_id=customer_id)
        conn_type = "customer"
        conn_id = str(customer.id)

    #* If neither authentication method worked, close the connection
    if not user and not customer:
        await websocket.close(code=status.WS_1008_POLICY_VIOLATION, reason="Authentication failed")
        await db.close()
        return

    #* Connect to the manager
    await manager.connect(websocket, conversation_id, conn_type, conn_id)

    try:
        await send_recent_messages(websocket, conversation_id)
        while True:
            data = await websocket.receive_text()
            message_data = json.loads(data)

            # Create message in DB
            message_to_create = MessageCreate(
                conversation_id=conversation_id,
                content=message_data.get("content"),
                sender=conn_type,
                message_type=message_data.get("message_type", "text"),
                asset_id=message_data.get("asset_id"),
                user_id=user.id if user else None,
                customer_id=customer.id if customer else None,
            )
            saved_message = await crud_chat.create_message(db, message=message_to_create)
            await db.refresh(saved_message, ['asset']) # Eagerly load asset for broadcast

            # Prepare broadcast message
            broadcast_message = {
                "id": saved_message.id,
                "type": "message",
                "content": saved_message.content,
                "sender": saved_message.sender,
                "message_type": saved_message.message_type,
                "asset_id": saved_message.asset_id,
                "asset_url": saved_message.asset.s3_url if saved_message.asset else None,
                "timestamp": saved_message.created_at.isoformat(),
                "user_info": { "name": user.full_name if user else customer.name or f"Visitor #{customer.id}" }
            }
            await manager.broadcast_to_conversation(json.dumps(broadcast_message), conversation_id)

            # Send notification to agents when customer sends a message
            if conn_type == "customer" and customer:
                await send_new_message_notification_if_available(
                    message_id=saved_message.id,
                    conversation_id=conversation_id,
                    customer_id=customer.id,
                    customer_name=customer.name or f"Customer #{customer.id}",
                    message_content=saved_message.content,
                    organization_id=customer.organization_id,
                    team_id=None  # Could be enhanced to include team info
                )

            # Trigger bot response if a customer message is sent and no agent is active
            if conn_type == "customer":
                if not await manager.is_agent_in_conversation(conversation_id):
                    await generate_bot_response(conversation_id, saved_message.content, db)

    except WebSocketDisconnect:
        manager.disconnect(websocket, conversation_id)
    finally:
        await db.close()

async def send_recent_messages(websocket: WebSocket, conversation_id: int, limit: int = 50):
    """Send recent messages to a newly connected client"""
    async with AsyncSessionLocal() as db:
        try:
            messages = await crud_chat.get_messages_by_conversation(db=db, conversation_id=conversation_id)

            # Get the last 'limit' messages and reverse for chronological order
            recent_messages = messages[-limit:] if len(messages) > limit else messages

            for message in recent_messages:
                message_data = {
                    "type": "message",
                    "id": message.id,
                    "conversation_id": conversation_id,
                    "content": message.content,
                    "sender": message.sender,
                    "message_type": message.message_type,
                    "timestamp": message.created_at.isoformat()
                }
                await websocket.send_text(json.dumps(message_data))

        except Exception as e:
            print(f"Error sending recent messages: {e}")

async def generate_bot_response(conversation_id: int, user_message: str, db: AsyncSession):
    """Generate and send bot response"""

    #* bot logic (ment to be replace with your AI/chatbot logic)
    bot_responses = [
        "Thank you for your message. How can I help you today?",
        "I understand your concern. Let me connect you with an agent.",
        "That's a great question! Let me find the information for you.",
        "I'm here to help. Could you provide more details?",
        "Thank you for contacting us. An agent will be with you shortly."
    ]

    #* Simple response based on keywords
    user_message_lower = user_message.lower()
    if "help" in user_message_lower or "support" in user_message_lower:
        bot_content = "I'm here to help! What specific issue are you facing?"
    elif "agent" in user_message_lower or "human" in user_message_lower:
        bot_content = "I'll connect you with a human agent right away."
    elif "price" in user_message_lower or "cost" in user_message_lower:
        bot_content = "For pricing information, please visit our website or speak with an agent."
    else:
        import random
        bot_content = random.choice(bot_responses)

    # Add small delay to simulate processing
    await asyncio.sleep(1)

    try:
        bot_message = MessageCreate(
            conversation_id=conversation_id,
            content=bot_content,
            sender="bot",
            message_type="text"
        )

        saved_bot_message = await crud_chat.create_message(db=db, message=bot_message)

        # Broadcast bot response
        bot_response = {
            "type": "message",
            "id": saved_bot_message.id,
            "conversation_id": conversation_id,
            "content": bot_content,
            "sender": "bot",
            "message_type": "text",
            "timestamp": saved_bot_message.created_at.isoformat()
        }

        await manager.broadcast_to_conversation(
            json.dumps(bot_response),
            conversation_id
        )

    except Exception as e:
        print(f"Error generating bot response: {e}")

# NEW: WebSocket endpoint for organization-wide notifications
@router.websocket("/agent-notifications")
async def websocket_agent_notifications(
    websocket: WebSocket,
    session_cookie: Optional[str] = Cookie(None, alias="yupcha_session")
):
    """
    A dedicated WebSocket for agents and admins to receive real-time
    notifications, such as new unassigned conversations.
    """
    # Authenticate the user using session cookie
    if not session_cookie:
        await websocket.close(code=status.WS_1008_POLICY_VIOLATION, reason="Authentication required")
        return

    try:
        # Verify session cookie
        user_id_str = serializer.loads(session_cookie, max_age=86400)
        user_id = int(user_id_str)
    except Exception:
        await websocket.close(code=status.WS_1008_POLICY_VIOLATION, reason="Invalid session")
        return

    # Get user from database
    async with AsyncSessionLocal() as db:
        user = await crud_user.get_user(db, user_id=user_id)
        if not user:
            await websocket.close(code=status.WS_1008_POLICY_VIOLATION, reason="User not found")
            return

        # Check if user is agent or admin
        if user.role not in [UserRole.agent, UserRole.admin]:
            await websocket.close(code=status.WS_1008_POLICY_VIOLATION, reason="Access denied")
            return

        if not user.organization_id:
            await websocket.close(code=status.WS_1008_POLICY_VIOLATION, reason="User not part of an organization")
            return

    organization_id = user.organization_id
    await manager.connect_to_org_channel(websocket, organization_id, user.id, user.role.value)

    try:
        # Send welcome message
        welcome_message = {
            "type": "notification_channel_connected",
            "organization_id": organization_id,
            "user_id": user.id,
            "message": "Connected to organization notification channel"
        }
        await websocket.send_text(json.dumps(welcome_message))

        # Keep connection alive and listen for any client messages (optional)
        while True:
            try:
                # This connection is primarily for receiving notifications,
                # but we can handle client messages if needed
                data = await websocket.receive_text()
                client_message = json.loads(data)

                # Handle ping/pong for connection health
                if client_message.get("type") == "ping":
                    await websocket.send_text(json.dumps({"type": "pong"}))

            except json.JSONDecodeError:
                # Ignore invalid JSON
                pass

    except WebSocketDisconnect:
        manager.disconnect_from_org_channel(websocket, organization_id)
    except Exception as e:
        print(f"Error in agent notifications WebSocket: {e}")
        manager.disconnect_from_org_channel(websocket, organization_id)

