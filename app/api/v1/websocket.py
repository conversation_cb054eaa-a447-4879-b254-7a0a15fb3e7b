import asyncio
from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Query, <PERSON>ie, status, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession
import json
import logging
from typing import Optional
from uuid import UUID
from pydantic import ValidationError, TypeAdapter

from app.schemas.websocket import IncomingMessage, PingMessageIn, TextMessageIn, MediaMessageIn, TypingIndicatorIn
from app.core.websocket_manager import manager
from app.core.rate_limiter import check_rate_limit
from app.db.session import AsyncSessionLocal
from app.crud import crud_chat, crud_user, crud_customer
from app.models.customer import Customer
from app.schemas.chat import MessageCreate, MessageSender
from app.models.user import User
from app.auth.dependencies import serializer
from app.core.config import settings

router = APIRouter()
logger = logging.getLogger(__name__)

IncomingMessageAdapter = TypeAdapter(IncomingMessage)

async def get_authenticated_user_from_cookie(
    session_cookie: Optional[str], db: AsyncSession
) -> Optional[User]:
    """Helper to safely authenticate a user from a session cookie for WebSockets."""
    if not session_cookie:
        return None
    try:
        user_id_str = serializer.loads(session_cookie, max_age=86400)
        user_id = UUID(user_id_str)
        return await crud_user.get_user(db, id=user_id) # Fixed: use get_user instead of get_user_with_roles
    except Exception:
        return None

@router.websocket("/chat/{conversation_id}")
async def websocket_chat(
    websocket: WebSocket,
    conversation_id: UUID,
    customer_id: Optional[str] = Query(None),
    session_cookie: Optional[str] = Cookie(None, alias=settings.SESSION_COOKIE_NAME),
):
    """
    Unified WebSocket endpoint for real-time chat.
    - Authenticated company users connect via session cookie.
    - Customers connect using a `customer_id`.
    """
    db: AsyncSession = AsyncSessionLocal()
    user: Optional[User] = None
    customer: Optional[Customer] = None
    identifier: Optional[str] = None
    listen_task: Optional[asyncio.Task] = None

    if session_cookie:
        user = await get_authenticated_user_from_cookie(session_cookie, db)
        if user:
            identifier = f"user:{user.id}"
    elif customer_id:
        customer = await crud_customer.get_or_create_customer(db, customer_id=customer_id)
        if customer:
            identifier = f"customer:{customer.id}"

    if not identifier:
        # We don't accept before closing if auth fails immediately
        return await websocket.close(code=status.WS_1008_POLICY_VIOLATION)

    try:
        await websocket.accept()
        listen_task = await manager.on_connect(websocket, conversation_id, user, customer)
        await send_recent_messages(websocket, conversation_id)

        while True:
            try:
                data = await websocket.receive_json()
                incoming_message = IncomingMessageAdapter.validate_python(data)

            except (ValidationError, json.JSONDecodeError) as e:
                logger.warning(f"Invalid WebSocket message from {identifier}: {e}")
                try:
                    await websocket.send_text(json.dumps({"type": "error", "detail": "Invalid message format."}))
                except Exception:
                    # WebSocket might be closed, break the loop
                    break
                continue
            except Exception as e:
                # Handle WebSocket disconnection or other connection errors
                logger.info(f"WebSocket connection closed for {identifier}: {e}")
                break
            
            if not await check_rate_limit(identifier):
                try:
                    await websocket.send_text(json.dumps({
                        "type": "error",
                        "detail": "Rate limit exceeded. Please slow down.",
                        "code": "RATE_LIMIT_EXCEEDED"
                    }))
                except Exception:
                    # WebSocket might be closed, break the loop
                    break
                continue

            # Process validated message
            if isinstance(incoming_message, (TextMessageIn, MediaMessageIn)):
                sender_type = MessageSender.AGENT if user else MessageSender.CUSTOMER
                message_to_create = MessageCreate(
                    conversation_id=conversation_id,
                    content=getattr(incoming_message, 'content', None),
                    sender=sender_type,
                    message_type=incoming_message.type,
                    asset_id=getattr(incoming_message, 'asset_id', None),
                    user_id=user.id if user else None,
                    customer_id=customer.id if customer else None,
                )
                saved_message = await crud_chat.create_message(db, message=message_to_create)
                await db.refresh(saved_message, ['asset', 'user'])

                broadcast_message = {
                    "id": str(saved_message.id),
                    "type": "message",
                    "content": saved_message.content,
                    "sender": saved_message.sender,
                    "message_type": saved_message.message_type,
                    "asset_id": str(saved_message.asset_id) if saved_message.asset_id else None,
                    "asset_url": saved_message.asset.s3_url if saved_message.asset else None,
                    "timestamp": saved_message.created_at.isoformat(),
                    "user_info": {"name": user.full_name if user else (customer.name or f"Visitor #{customer.id}")}
                }

                channel = manager.get_chat_channel(conversation_id)
                await manager.broadcast(channel, json.dumps(broadcast_message))
                
                if sender_type == MessageSender.CUSTOMER and not await manager.is_user_active(conversation_id):
                    asyncio.create_task(generate_bot_response(conversation_id))

            elif isinstance(incoming_message, TypingIndicatorIn):
                typing_event = {
                    "type": "typing",
                    "is_typing": incoming_message.is_typing,
                    "user_info": {"name": user.full_name if user else (customer.name or f"Visitor #{customer.id}")}
                }
                channel = manager.get_chat_channel(conversation_id)
                await manager.broadcast(channel, json.dumps(typing_event), broadcast_to_sender=False, sender_websocket=websocket)

    except WebSocketDisconnect:
        pass
    except Exception as e:
        logger.error(f"Error in WebSocket chat for conversation {conversation_id}: {e}", exc_info=True)
    finally:
        if listen_task:
            await manager.on_disconnect(websocket, conversation_id, user, listen_task)
        await db.close()


async def send_recent_messages(websocket: WebSocket, conversation_id: UUID, limit: int = 50):
    """Sends recent messages to a newly connected client."""
    async with AsyncSessionLocal() as db:
        try:
            messages = await crud_chat.get_messages_by_conversation(db, conversation_id=conversation_id, limit=limit)
            for message in reversed(messages): # Send oldest first
                message_data = {
                    "type": "message",
                    "id": str(message.id),
                    "content": message.content,
                    "sender": message.sender,
                    "message_type": message.message_type,
                    "timestamp": message.created_at.isoformat(),
                    "asset_url": message.asset.s3_url if message.asset else None,
                }
                await websocket.send_text(json.dumps(message_data))
        except Exception as e:
            logger.error(f"Error sending recent messages: {e}")

# This function is now self-contained and correct.
async def generate_bot_response(conversation_id: UUID):
    """Generate and send bot response."""
    bot_responses = [
        "Thank you for your message. How can I help you today?",
        "I understand your concern. An agent will be with you shortly.",
        "That's a great question! Let me find some information for you.",
    ]
    await asyncio.sleep(1.5) # Simulate thinking
    import random
    bot_content = random.choice(bot_responses)

    # This function now correctly creates its own DB session.
    async with AsyncSessionLocal() as db:
        try:
            bot_message_to_create = MessageCreate(
                conversation_id=conversation_id,
                content=bot_content,
                sender=MessageSender.BOT,
                message_type="text"
            )
            saved_bot_message = await crud_chat.create_message(db=db, message=bot_message_to_create)

            # --- FIX: MOVED THIS BLOCK INSIDE THE `try` BLOCK ---
            bot_response_payload = {
                "type": "message",
                "id": str(saved_bot_message.id),
                "content": saved_bot_message.content,
                "sender": saved_bot_message.sender,
                "message_type": saved_bot_message.message_type,
                "timestamp": saved_bot_message.created_at.isoformat(),
                "user_info": {"name": "Yupcha Bot"}
            }
            channel = manager.get_chat_channel(conversation_id)
            await manager.broadcast(channel, json.dumps(bot_response_payload))
            # ----------------------------------------------------

        except Exception as e:
            logger.error(f"Error in generate_bot_response: {e}")
            # Don't re-raise, just log it. We don't want a failed bot
            # response to crash the server.

# --- user-notifications endpoint (no changes needed, it's correct) ---
@router.websocket("/user-notifications") 
async def websocket_user_notifications(
    websocket: WebSocket,
    session_cookie: Optional[str] = Cookie(None, alias=settings.SESSION_COOKIE_NAME)
):
    # ... (Your existing correct code for this endpoint)
    """
    A dedicated WebSocket for any authenticated company user (agents, admins, etc.) 
    to receive real-time notifications relevant to their organization and team.
    """
    # Authenticate the user using the session cookie
    if not session_cookie:
        await websocket.close(code=status.WS_1008_POLICY_VIOLATION, reason="Authentication required")
        return

    try:
        user_id_str = serializer.loads(session_cookie, max_age=86400)
        user_id = UUID(user_id_str)
    except Exception:
        await websocket.close(code=status.WS_1008_POLICY_VIOLATION, reason="Invalid session")
        return

    # Get user from the database
    async with AsyncSessionLocal() as db:
        user = await crud_user.get_user(db, id=user_id)
        
        # check if the user is valid and belongs to an organization.
        
        if not user or not user.is_active:
            await websocket.close(code=status.WS_1008_POLICY_VIOLATION, reason="User not found or inactive")
            return

        if not user.organization_id:
            await websocket.close(code=status.WS_1008_POLICY_VIOLATION, reason="User is not part of an organization")
            return

    organization_id = user.organization_id
    
    
    await manager.connect_to_org_channel(websocket, organization_id, user.id, "user")

    try:
        await websocket.send_text(json.dumps({
            "type": "notification_channel_connected",
            "message": "Connected successfully to notifications."
        }))

        # Createing a unique identifier for this user's notification socket for rate limiting
        notification_identifier = f"notifications:user:{user.id}"

        while True:
            try:
                data = await websocket.receive_json()
                client_message = PingMessageIn.model_validate(data) # Only expecting ping for now

                if not await check_rate_limit(notification_identifier):
                    error_message = {
                        "type": "error",
                        "detail": "Rate limit exceeded on notification channel.",
                        "code": "RATE_LIMIT_EXCEEDED"
                    }
                    try:
                        await websocket.send_json(error_message)
                    except:
                        pass 
                    continue 

                if client_message.type == "ping":
                    await websocket.send_json({"type": "pong"})

            except ValidationError as e:
                logger.warning(f"Invalid message format on notification socket from user {user.id}: {e.errors()}")
                error_message = {
                    "type": "error",
                    "detail": "Invalid message format",
                    "errors": e.errors()
                }
                await websocket.send_json(error_message)
                continue
            except json.JSONDecodeError:
                logger.warning(f"Invalid JSON received on notification socket from user {user.id}")
                error_message = {
                    "type": "error",
                    "detail": "Invalid JSON format"
                }
                await websocket.send_json(error_message)
                continue
            except WebSocketDisconnect:
                break 
            except Exception as e:
                logger.error(f"Error in user notifications WebSocket loop for user {user.id}: {e}")
                break

    finally:
        logger.info(f"User {user.id} disconnected from notifications for organization {organization_id}")
        manager.disconnect_from_org_channel(websocket, organization_id)