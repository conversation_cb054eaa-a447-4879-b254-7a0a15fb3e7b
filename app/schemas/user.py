from pydantic import BaseModel, EmailStr, Field
from typing import Optional, List
from datetime import datetime

# Shared properties
class UserBase(BaseModel):
    email: EmailStr
    full_name: Optional[str] = None
    organization_id: Optional[int] = None
    team_id: Optional[int] = None
    is_active: bool = True

# Properties to receive via API on creation
class UserCreate(UserBase):
    password: str
    role_names: List[str] = Field(default_factory=list, description="List of role names to assign (e.g., ['Admin', 'Agent'])")

# Properties to receive via API on update
class UserUpdate(BaseModel):
    full_name: Optional[str] = None
    email: Optional[EmailStr] = None
    team_id: Optional[int] = None
    is_active: Optional[bool] = None
    # Role updates are now handled by the dedicated /api/roles/assign endpoint

# Properties to return to client
class UserResponse(UserBase):
    id: int
    roles: List[dict] = []  # Will be populated with role information
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True

# For backward compatibility
User = UserResponse
