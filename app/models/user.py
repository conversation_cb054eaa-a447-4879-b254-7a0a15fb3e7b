from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, DateTime
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from app.db.base import Base
from app.models.role import user_role_association

class User(Base):
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    full_name = Column(String(255), index=True)
    email = Column(String(255), unique=True, index=True, nullable=False)
    hashed_password = Column(String(255), nullable=False)

    # OLD FIELDS REMOVED: The 'role' and 'is_admin' columns are no longer needed.
    # The Alembic migration will handle dropping these from the database.

    organization_id = Column(Integer, ForeignKey("organizations.id"), index=True, nullable=True)
    team_id = Column(Integer, ForeignKey("teams.id"), index=True, nullable=True)
    is_active = Column(Boolean, default=True, nullable=False)
    is_deleted = Column(Boolean, default=False, nullable=False, index=True)
    deleted_at = Column(DateTime(timezone=True), nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Many-to-many relationship with Role
    roles = relationship(
        "Role",
        secondary=user_role_association,
        back_populates="users",
        lazy="selectin",  # Efficient loading of roles
        foreign_keys=[user_role_association.c.user_id, user_role_association.c.role_id]
    )

    # Relationships
    organization = relationship("Organization", back_populates="users")
    team = relationship("Team", back_populates="users")
    messages = relationship("Message", back_populates="user")
    canned_responses = relationship("CannedResponse", back_populates="creator")  # NEW

    def has_role(self, role_name: str) -> bool:
        """Check if user has a specific role"""
        return any(role.name == role_name for role in self.roles)

    def get_role_names(self) -> set[str]:
        """Get all role names for this user"""
        return {role.name for role in self.roles}

    def is_admin_user(self) -> bool:
        """Check if user has admin role"""
        return self.has_role("Admin")

    def is_agent_user(self) -> bool:
        """Check if user has agent role (all users should have this)"""
        return self.has_role("Agent")

    def has_admin_privileges(self) -> bool:
        """Check if user has admin privileges (same as is_admin_user but clearer name)"""
        return self.is_admin_user()

    def get_primary_role(self) -> str:
        """Get the primary role for display purposes - returns highest priority role"""
        if not self.roles:
            return "Agent"  # Default fallback

        # Define role priority (higher number = higher priority)
        role_priorities = {
            "Admin": 100,
            "Manager": 80,
            "Supervisor": 60,
            "HR": 40,
            "Agent": 20
        }

        # Find the role with highest priority
        highest_priority = 0
        primary_role = "Agent"

        for role in self.roles:
            priority = role_priorities.get(role.name, 10)  # Unknown roles get low priority
            if priority > highest_priority:
                highest_priority = priority
                primary_role = role.name

        return primary_role
