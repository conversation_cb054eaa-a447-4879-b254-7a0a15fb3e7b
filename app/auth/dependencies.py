from fastapi import Depends, HTTPException, status, <PERSON><PERSON>
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload
from itsdangerous import URLSafeTimedSerializer, SignatureExpired, BadTimeSignature
from typing import Optional, Union, List

from app.core.config import settings
from app.db.session import get_async_db
from app.crud import crud_user
from app.models.user import User

SESSION_COOKIE_NAME = settings.SESSION_COOKIE_NAME
serializer = URLSafeTimedSerializer(settings.SECRET_KEY)

async def get_current_user(
    session_cookie: Optional[str] = <PERSON><PERSON>(None, alias=SESSION_COOKIE_NAME),
    db: AsyncSession = Depends(get_async_db),
) -> User:
    """
    Dependency to get the current, active, non-deleted user from the session cookie.
    This is the single source of truth for user authentication.
    """
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    
    if session_cookie is None:
        raise credentials_exception
        
    try:
        user_id = int(serializer.loads(session_cookie, max_age=86400))
    except (SignatureExpired, BadTimeSignature, ValueError):
        raise credentials_exception

    # Fetch user with roles, including deleted ones, to provide specific feedback
    user = await crud_user.get_user_with_roles(db, user_id=user_id, include_deleted=True)

    if user is None:
        raise credentials_exception

    # Additional checks for active and non-deleted status
    if not user.is_active:
        raise HTTPException(status_code=400, detail="Inactive user")
    if user.is_deleted:
        raise HTTPException(status_code=400, detail="User account has been deleted")

    return user

# Legacy dependencies for backward compatibility
def get_current_active_admin(current_user: User = Depends(get_current_user)) -> User:
    """Dependency to ensure the user has admin privileges (legacy)."""
    if not current_user.has_role("Admin"):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="The user doesn't have enough privileges (admin required)",
        )
    return current_user

def get_current_active_agent(current_user: User = Depends(get_current_user)) -> User:
    """Dependency to ensure the user is an active agent (legacy)."""
    if not current_user.has_role("Agent"):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="The user doesn't have enough privileges (agent required)",
        )
    return current_user

# NEW: Dynamic RBAC Dependencies
def require_role(required_role: str):
    """
    Dependency generator that checks if the current user has a specific role.
    This is the new, flexible way to protect endpoints.

    Usage:
        @router.get("/admin-only", dependencies=[Depends(require_role("Admin"))])
        or
        @router.get("/admin-only")
        async def admin_endpoint(current_user: User = Depends(require_role("Admin"))):
    """
    async def role_checker(current_user: User = Depends(get_current_user)) -> User:
        if not current_user.has_role(required_role):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Access denied. Required role: '{required_role}'",
            )
        return current_user
    return role_checker

def require_any_role(*required_roles: str):
    """
    Dependency generator that checks if the current user has ANY of the specified roles.

    Usage:
        @router.get("/manager-or-admin")
        async def endpoint(current_user: User = Depends(require_any_role("Admin", "Manager"))):
    """
    async def role_checker(current_user: User = Depends(get_current_user)) -> User:
        user_roles = current_user.get_role_names()
        if not any(role in user_roles for role in required_roles):
            roles_str = "', '".join(required_roles)
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Access denied. Required roles: '{roles_str}'",
            )
        return current_user
    return role_checker

def require_all_roles(*required_roles: str):
    """
    Dependency generator that checks if the current user has ALL of the specified roles.

    Usage:
        @router.get("/super-admin")
        async def endpoint(current_user: User = Depends(require_all_roles("Admin", "HR"))):
    """
    async def role_checker(current_user: User = Depends(get_current_user)) -> User:
        user_roles = current_user.get_role_names()
        if not all(role in user_roles for role in required_roles):
            roles_str = "', '".join(required_roles)
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Access denied. Required roles: '{roles_str}'",
            )
        return current_user
    return role_checker

# Convenience dependencies for common roles
def require_admin():
    """Convenience dependency for Admin role"""
    return require_role("Admin")

def require_manager():
    """Convenience dependency for Manager role"""
    return require_role("Manager")

def require_agent():
    """Convenience dependency for Agent role"""
    return require_role("Agent")

def require_supervisor():
    """Convenience dependency for Supervisor role"""
    return require_role("Supervisor")

def require_hr():
    """Convenience dependency for HR role"""
    return require_role("HR")

async def get_current_user_from_websocket_token(
    token: str,
    db: AsyncSession
) -> Optional[User]:
    """Get current user from WebSocket token parameter"""
    try:
        # Decode the session token
        user_id = serializer.loads(token, max_age=86400)  # 24 hours

        # Get user from database
        user = await crud_user.get_user(db, user_id=user_id)

        if not user:
            return None

        if not user.is_active:
            return None

        if user.is_deleted:
            return None

        return user

    except Exception:
        return None