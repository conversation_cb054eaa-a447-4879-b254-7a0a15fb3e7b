from fastapi import Depends, HTTPException, status, <PERSON><PERSON>
from sqlalchemy.ext.asyncio import AsyncSession
from itsdangerous import URLSafeTimedSerializer, SignatureExpired, BadTimeSignature
from typing import Optional

from app.core.config import settings
from app.db.session import get_async_db
from app.crud import crud_user
from app.models.user import User, UserRole

SESSION_COOKIE_NAME = settings.SESSION_COOKIE_NAME
serializer = URLSafeTimedSerializer(settings.SECRET_KEY)

async def get_current_user(
    session_cookie: Optional[str] = <PERSON>ie(None, alias=SESSION_COOKIE_NAME),
    db: AsyncSession = Depends(get_async_db),
) -> User:
    """
    Dependency to get the current, active, non-deleted user from the session cookie.
    This is the single source of truth for user authentication.
    """
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    
    if session_cookie is None:
        raise credentials_exception
        
    try:
        user_id = int(serializer.loads(session_cookie, max_age=86400))
    except (SignatureExpired, BadTimeSignature, ValueError):
        raise credentials_exception

    # Fetch user, including deleted ones, to provide specific feedback
    user = await crud_user.get_user(db, user_id=user_id, include_deleted=True)
    
    if user is None:
        raise credentials_exception
        
    # NEW: Add checks for active and non-deleted status
    if not user.is_active:
        raise HTTPException(status_code=400, detail="Inactive user")
    if user.is_deleted:
        raise HTTPException(status_code=400, detail="User account has been deleted")
        
    return user

def get_current_active_admin(current_user: User = Depends(get_current_user)) -> User:
    """Dependency to ensure the user is an active admin."""
    if current_user.role != UserRole.admin:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="The user doesn't have enough privileges (admin required)",
        )
    return current_user

def get_current_active_agent(current_user: User = Depends(get_current_user)) -> User:
    """Dependency to ensure the user is an active agent or admin."""
    if current_user.role not in [UserRole.agent, UserRole.admin]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="The user doesn't have enough privileges (agent or admin required)",
        )
    return current_user