from fastapi import Depends, HTTPException, status, <PERSON><PERSON>
from sqlalchemy.ext.asyncio import AsyncSession
from itsdangerous import URLSafeTimedSerializer, SignatureExpired, BadTimeSignature
from typing import Optional

from app.core.config import settings
from app.db.session import get_async_db
from app.crud import crud_user
from app.models.user import User, UserRole

# This is the name of the cookie we will set
SESSION_COOKIE_NAME = "yupcha_session"

# Serializer to sign and verify our session data (user ID)
serializer = URLSafeTimedSerializer(settings.SECRET_KEY)

async def get_current_user(
    session_cookie: Optional[str] = <PERSON><PERSON>(None, alias=SESSION_COOKIE_NAME),
    db: AsyncSession = Depends(get_async_db),
) -> User:
    """
    Dependency to get the current user from the session cookie.
    Raises 401 Unauthorized if the user is not authenticated.
    """
    if session_cookie is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Not authenticated",
        )
    try:
        # The max_age is in seconds. Let's set it to 1 day.
        user_id_str = serializer.loads(session_cookie, max_age=86400)
        user_id = int(user_id_str)
    except (SignatureExpired, BadTimeSignature):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid or expired session",
        )

    user = await crud_user.get_user(db, user_id=user_id)
    if user is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="User not found",
        )
    return user

def get_current_active_admin(current_user: User = Depends(get_current_user)) -> User:
    """Dependency to ensure the user is an active admin."""
    if current_user.role != UserRole.admin:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="The user doesn't have enough privileges (admin required)",
        )
    return current_user

def get_current_active_agent(current_user: User = Depends(get_current_user)) -> User:
    """Dependency to ensure the user is an active agent or admin."""
    if current_user.role not in [UserRole.agent, UserRole.admin]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="The user doesn't have enough privileges (agent required)",
        )
    return current_user
