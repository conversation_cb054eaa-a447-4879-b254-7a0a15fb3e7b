from datetime import timed<PERSON><PERSON>
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, delete
from typing import List, Optional

from app.models.team import Team
from app.schemas.team import TeamCreate, TeamUpdate

async def create_team(db: AsyncSession, team: TeamCreate) -> Team:
    """Create a new team"""
    db_team = Team(**team.model_dump())
    db.add(db_team)
    await db.commit()
    await db.refresh(db_team)
    return db_team

async def get_team(db: AsyncSession, team_id: int, include_deleted: bool = False) -> Optional[Team]:
    """Get a team by ID, optionally including soft-deleted ones."""
    stmt = select(Team).where(Team.id == team_id)
    #* MODIFIED: Conditionally filter out deleted records
    if not include_deleted:
        stmt = stmt.where(Team.is_deleted == False)
    result = await db.execute(stmt)
    return result.scalar_one_or_none()

async def get_teams(db: AsyncSession, skip: int = 0, limit: int = 100) -> List[Team]:
    """Get all non-deleted teams with pagination."""
    #* MODIFIED: Always filter out deleted records
    stmt = select(Team).where(Team.is_deleted == False).offset(skip).limit(limit)
    result = await db.execute(stmt)
    return result.scalars().all()

async def get_teams_by_organization(db: AsyncSession, organization_id: int, skip: int = 0, limit: int = 100) -> List[Team]:
    """Get teams by organization"""
    result = await db.execute(
        select(Team)
        .filter(Team.organization_id == organization_id)
        .offset(skip).limit(limit)
    )
    return result.scalars().all()

async def update_team(db: AsyncSession, team_id: int, team_update: TeamUpdate) -> Optional[Team]:
    """Update a team"""
    team = await get_team(db, team_id)
    if team:
        update_data = team_update.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(team, field, value)
        await db.commit()
        await db.refresh(team)
    return team

async def delete_team(db: AsyncSession, team_id: int) -> bool:
    """Delete a team"""
    team = await get_team(db, team_id)
    if team:
        await db.delete(team)
        await db.commit()
        return True
    return False

async def soft_delete_team(db: AsyncSession, team_id: int) -> Optional[Team]:
    """Soft delete a team by setting is_deleted to True."""
    team = await get_team(db, team_id=team_id)
    if team:
        team.is_deleted = True
        team.deleted_at = datetime.now()
        await db.commit()
        await db.refresh(team)
    return team

async def restore_team(db: AsyncSession, team_id: int) -> Optional[Team]:
    """Restore a soft-deleted team."""
    team = await get_team(db, team_id=team_id, include_deleted=True)
    if team and team.is_deleted:
        team.is_deleted = False
        team.deleted_at = None
        await db.commit()
        await db.refresh(team)
    return team

async def purge_deleted_teams(db: AsyncSession) -> int:
    """Permanently delete teams soft-deleted more than 90 days ago."""
    purge_date = datetime.utcnow() - timedelta(days=90)
    stmt = delete(Team).where(Team.is_deleted == True, Team.deleted_at <= purge_date)
    result = await db.execute(stmt)
    await db.commit()
    return result.rowcount