from datetime import datetime, timezone
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update
from sqlalchemy.orm import selectinload
from typing import List, Optional

from app.models.chat import Conversation, Message, ConversationStatus
from app.models.organization import Organization
from app.schemas.chat import Conversation<PERSON><PERSON>, MessageCreate
from fastapi import HTT<PERSON>Exception


async def touch_conversation(db: AsyncSession, conversation_id: int):
    """Explicitly update the updated_at timestamp for a conversation."""
    stmt = (
        update(Conversation)
        .where(Conversation.id == conversation_id)
        .values(updated_at=datetime.now(timezone.utc))
    )
    await db.execute(stmt)
    # Note: Don't commit here, let the caller handle the commit
    
# Conversation CRUD operations
async def create_conversation(db: AsyncSession, conversation: ConversationCreate) -> Conversation:
    """Create a new conversation and auto-assign to the organization's default team."""

    # Get the organization to find its default team
    org = await db.get(Organization, conversation.organization_id)
    if not org:
        raise HTTPException(status_code=404, detail="Organization not found")

    db_conversation = Conversation(
        **conversation.model_dump(),
        assigned_team_id=org.default_team_id,  # Auto-assign
        status=ConversationStatus.open if org.default_team_id else ConversationStatus.new
    )
    db.add(db_conversation)
    await db.commit()
    await db.refresh(db_conversation)
    return db_conversation

async def get_conversation(db: AsyncSession, conversation_id: int) -> Optional[Conversation]:
    """Get a conversation by ID, with related data"""
    result = await db.execute(
        select(Conversation)
        .options(
            selectinload(Conversation.assigned_team),
            selectinload(Conversation.customer),
            selectinload(Conversation.organization)
        )
        .filter(Conversation.id == conversation_id)
    )
    return result.scalar_one_or_none()

async def get_conversations(db: AsyncSession, skip: int = 0, limit: int = 100) -> List[Conversation]:
    """Get all conversations with pagination, ordered by most recently updated first"""
    result = await db.execute(
        select(Conversation)
        .order_by(Conversation.updated_at.desc().nulls_last(), Conversation.created_at.desc())
        .offset(skip)
        .limit(limit)
    )
    return result.scalars().all()

async def get_conversations_by_user(db: AsyncSession, user_id: str, skip: int = 0, limit: int = 100) -> List[Conversation]:
    """Get conversations for a specific user"""
    result = await db.execute(
        select(Conversation).filter(Conversation.user_id == user_id).offset(skip).limit(limit)
    )
    return result.scalars().all()

async def update_conversation_status(db: AsyncSession, conversation_id: int, status: str) -> Optional[Conversation]:
    """Update conversation status"""
    result = await db.execute(select(Conversation).filter(Conversation.id == conversation_id))
    db_conversation = result.scalar_one_or_none()
    if db_conversation:
        db_conversation.status = status
        await db.commit()
        await db.refresh(db_conversation)
    return db_conversation

# Message CRUD operations
async def create_message(db: AsyncSession, message: MessageCreate) -> Message:
    """Create a new message and 'touch' the parent conversation to update its timestamp."""
    db_message = Message(**message.model_dump())
    db.add(db_message)
    
    # Touch the parent conversation to update its 'updated_at' field
    await touch_conversation(db, message.conversation_id)
    
    # Commit both the new message and the conversation update
    await db.commit()
    await db.refresh(db_message)
    return db_message


async def get_message(db: AsyncSession, message_id: int) -> Optional[Message]:
    """Get a message by ID"""
    result = await db.execute(select(Message).filter(Message.id == message_id))
    return result.scalar_one_or_none()

async def get_messages_by_conversation(db: AsyncSession, conversation_id: int) -> List[Message]:
    """Get all messages for a conversation"""
    result = await db.execute(
        select(Message).filter(Message.conversation_id == conversation_id).order_by(Message.created_at)
    )
    return result.scalars().all()

async def get_recent_messages(db: AsyncSession, conversation_id: int, limit: int = 10) -> List[Message]:
    """Get recent messages for a conversation"""
    result = await db.execute(
        select(Message)
        .filter(Message.conversation_id == conversation_id)
        .order_by(Message.created_at.desc())
        .limit(limit)
    )
    return result.scalars().all()

async def assign_team_to_conversation(db: AsyncSession, conversation_id: int, team_id: int) -> Optional[Conversation]:
    """Assigns a team to a conversation and updates its status"""
    conversation = await get_conversation(db, conversation_id=conversation_id)
    if conversation:
        conversation.assigned_team_id = team_id
        conversation.status = ConversationStatus.open  # Change status from 'new' to 'open'
        await db.commit()
        await db.refresh(conversation)
    return conversation

async def get_conversations_by_team(db: AsyncSession, team_id: int, skip: int = 0, limit: int = 100) -> List[Conversation]:
    """Get conversations assigned to a specific team, ordered by most recently updated first"""
    result = await db.execute(
        select(Conversation)
        .options(selectinload(Conversation.assigned_team))
        .filter(Conversation.assigned_team_id == team_id)
        .order_by(Conversation.updated_at.desc().nulls_last(), Conversation.created_at.desc())
        .offset(skip).limit(limit)
    )
    return result.scalars().all()

async def get_unassigned_conversations(db: AsyncSession, skip: int = 0, limit: int = 100) -> List[Conversation]:
    """Get conversations that are not assigned to any team, ordered by most recently updated first"""
    result = await db.execute(
        select(Conversation)
        .filter(Conversation.assigned_team_id.is_(None))
        .filter(Conversation.status == ConversationStatus.new)
        .order_by(Conversation.updated_at.desc().nulls_last(), Conversation.created_at.desc())
        .offset(skip).limit(limit)
    )
    return result.scalars().all()

async def get_conversations_by_organization(db: AsyncSession, organization_id: int, skip: int = 0, limit: int = 100) -> List[Conversation]:
    """Get conversations for a specific organization"""
    result = await db.execute(
        select(Conversation)
        .filter(Conversation.organization_id == organization_id)
        .offset(skip).limit(limit)
    )
    return result.scalars().all()

async def get_conversations_by_customer(db: AsyncSession, customer_id: int, skip: int = 0, limit: int = 100) -> List[Conversation]:
    """Get conversations for a specific customer"""
    result = await db.execute(
        select(Conversation)
        .filter(Conversation.customer_id == customer_id)
        .offset(skip).limit(limit)
    )
    return result.scalars().all()

# Message-specific CRUD operations

async def get_message(db: AsyncSession, message_id: int) -> Optional[Message]:
    """Get a message by ID, including its conversation for context."""
    result = await db.execute(
        select(Message)
        .options(selectinload(Message.conversation))
        .where(Message.id == message_id)
    )
    return result.scalar_one_or_none()

async def soft_delete_message(db: AsyncSession, message_id: int) -> Optional[Message]:
    """Soft deletes a message by setting its 'deleted' flag to True."""
    message = await get_message(db, message_id=message_id)
    if message:
        message.deleted = True
        await db.commit()
        await db.refresh(message)
    return message

async def restore_message(db: AsyncSession, message_id: int) -> Optional[Message]:
    """Restores a soft-deleted message by setting its 'deleted' flag to False."""
    message = await get_message(db, message_id=message_id)
    if message:
        message.deleted = False
        await db.commit()
        await db.refresh(message)
    return message

async def get_messages_with_deleted(db: AsyncSession, conversation_id: int, include_deleted: bool = False) -> List[Message]:
    """Get all messages for a conversation, optionally including deleted ones."""
    query = select(Message).filter(Message.conversation_id == conversation_id)

    if not include_deleted:
        query = query.filter(Message.deleted == False)

    query = query.order_by(Message.created_at)
    result = await db.execute(query)
    return result.scalars().all()

async def get_conversations_by_customer(db: AsyncSession, customer_id: int) -> List[Conversation]:
    """Get all conversations for a specific customer."""
    stmt = select(Conversation).where(Conversation.customer_id == customer_id).order_by(Conversation.created_at.desc())
    result = await db.execute(stmt)
    return result.scalars().all()