from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from typing import List, Optional

from app.models.user import User, UserRole
from app.schemas.user import UserCreate, UserUpdate
from app.core.security import get_password_hash

async def create_user(db: AsyncSession, user: UserCreate) -> User:
    """Create a new user (admin or agent) with a hashed password."""
    hashed_password = get_password_hash(user.password)
    db_user = User(
        email=user.email,
        full_name=user.full_name,
        hashed_password=hashed_password,
        role=user.role,
        organization_id=user.organization_id,
        team_id=user.team_id,
        is_active=user.is_active
    )
    db.add(db_user)
    await db.commit()
    await db.refresh(db_user)
    return db_user

async def get_user(db: AsyncSession, user_id: int, include_deleted: bool = False) -> Optional[User]:
    """Get a single user by ID, optionally including soft-deleted ones."""
    stmt = select(User).where(User.id == user_id)
    if not include_deleted:
        stmt = stmt.where(User.is_deleted == False)
    result = await db.execute(stmt)
    return result.scalar_one_or_none()

async def get_user_by_email(db: AsyncSession, email: str) -> Optional[User]:
    """Get a single user by email"""
    result = await db.execute(select(User).filter(User.email == email))
    return result.scalar_one_or_none()

async def get_users_by_role(db: AsyncSession, role: UserRole, skip: int = 0, limit: int = 100) -> List[User]:
    """Get all users of a specific role"""
    result = await db.execute(
        select(User).filter(User.role == role).offset(skip).limit(limit)
    )
    return result.scalars().all()

async def get_all_users(db: AsyncSession, skip: int = 0, limit: int = 100) -> List[User]:
    """Get all users"""
    result = await db.execute(select(User).offset(skip).limit(limit))
    return result.scalars().all()

async def update_user(db: AsyncSession, user_id: int, user_update: UserUpdate) -> Optional[User]:
    """Update a user with the provided data."""
    user = await get_user(db, user_id=user_id)
    if not user:
        return None

    # Update only the fields that are provided
    update_data = user_update.model_dump(exclude_unset=True)
    for field, value in update_data.items():
        setattr(user, field, value)

    await db.commit()
    await db.refresh(user)
    return user

# --- NEW: Soft Delete and Restore ---
async def soft_delete_user(db: AsyncSession, user_id: int) -> Optional[User]:
    """Soft delete a user by setting is_deleted to True."""
    user = await get_user(db, user_id=user_id)
    if user:
        user.is_deleted = True
        user.deleted_at = datetime.utcnow()
        await db.commit()
        await db.refresh(user)
    return user

async def restore_user(db: AsyncSession, user_id: int) -> Optional[User]:
    """Restore a soft-deleted user."""
    user = await get_user(db, user_id=user_id, include_deleted=True)
    if user and user.is_deleted:
        user.is_deleted = False
        user.deleted_at = None
        await db.commit()
        await db.refresh(user)
    return user

# --- NEW: Scheduled Purge Function ---
async def purge_deleted_users(db: AsyncSession) -> int:
    """Permanently delete users soft-deleted more than 90 days ago."""
    from datetime import timedelta
    purge_date = datetime.utcnow() - timedelta(days=90)
    
    stmt = select(User).where(User.is_deleted == True, User.deleted_at <= purge_date)
    users_to_purge = (await db.execute(stmt)).scalars().all()

    if not users_to_purge:
        return 0

    for user in users_to_purge:
        await db.delete(user)
    
    await db.commit()
    return len(users_to_purge)