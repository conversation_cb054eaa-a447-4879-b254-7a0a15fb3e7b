#!/usr/bin/env python3
"""
Test script for real-time notifications
"""

import asyncio
import websockets
import json
import requests
from datetime import datetime

BASE_URL = "http://localhost:8000/api"
WS_URL = "ws://localhost:8000/api/notifications/ws/notifications"

async def test_notification_websocket():
    """Test the notification WebSocket connection"""
    print("🔔 Testing Notification WebSocket...")
    
    # First, login to get a token
    print("🔐 Logging in...")
    login_data = {
        "username": "<EMAIL>",
        "password": "adminpassword"
    }
    
    response = requests.post(f"{BASE_URL}/auth/login", data=login_data)
    if response.status_code != 200:
        print(f"❌ Login failed: {response.status_code} - {response.text}")
        return
    
    token = response.json().get("access_token")
    print(f"✅ Login successful! Token: {token[:20]}...")
    
    # Connect to notification WebSocket
    ws_url_with_token = f"{WS_URL}?token={token}"
    print(f"🔌 Connecting to: {ws_url_with_token}")
    
    try:
        async with websockets.connect(ws_url_with_token) as websocket:
            print("✅ Connected to notification WebSocket!")
            
            # Listen for notifications
            print("👂 Listening for notifications...")
            
            # Send a ping to test connection
            await websocket.send(json.dumps({"type": "ping"}))
            print("📤 Sent ping")
            
            # Listen for messages for 30 seconds
            timeout = 30
            start_time = datetime.now()
            
            while (datetime.now() - start_time).seconds < timeout:
                try:
                    # Wait for message with timeout
                    message = await asyncio.wait_for(websocket.recv(), timeout=2.0)
                    data = json.loads(message)
                    
                    print(f"📨 Received: {data.get('type', 'unknown')} - {data.get('title', 'No title')}")
                    
                    if data.get("type") == "pong":
                        print("🏓 Pong received!")
                    elif data.get("type") == "system_alert":
                        print(f"🔔 System Alert: {data.get('message')}")
                    elif data.get("type") == "new_message":
                        print(f"💬 New Message: {data.get('message')}")
                        print(f"   From: {data.get('data', {}).get('customer_name', 'Unknown')}")
                    
                except asyncio.TimeoutError:
                    # No message received, continue listening
                    continue
                except json.JSONDecodeError as e:
                    print(f"⚠️ Invalid JSON received: {e}")
                except Exception as e:
                    print(f"❌ Error receiving message: {e}")
                    break
            
            print(f"⏰ Listening timeout reached ({timeout}s)")
            
    except Exception as e:
        print(f"❌ WebSocket connection failed: {e}")

async def send_test_notification():
    """Send a test notification via API"""
    print("\n🧪 Sending test notification...")
    
    # Login first
    login_data = {
        "username": "<EMAIL>",
        "password": "adminpassword"
    }
    
    response = requests.post(f"{BASE_URL}/auth/login", data=login_data)
    if response.status_code != 200:
        print(f"❌ Login failed: {response.status_code}")
        return
    
    token = response.json().get("access_token")
    headers = {"Authorization": f"Bearer {token}"}
    
    # Send test notification
    response = requests.post(f"{BASE_URL}/notifications/test", headers=headers)
    
    if response.status_code == 200:
        print("✅ Test notification sent successfully!")
        print(f"Response: {response.json()}")
    else:
        print(f"❌ Failed to send test notification: {response.status_code} - {response.text}")

async def test_chat_notifications():
    """Test notifications by sending a chat message"""
    print("\n💬 Testing chat message notifications...")
    
    # This will simulate a customer sending a message
    # which should trigger a notification to agents
    
    customer_ws_url = "ws://localhost:8000/api/ws/chat/1?customer_id=test-notification-customer"
    
    try:
        async with websockets.connect(customer_ws_url) as websocket:
            print("✅ Connected as customer to chat WebSocket")
            
            # Send a message
            message = {
                "content": "Hello! I need help with my order. This should trigger a notification!",
                "type": "text"
            }
            
            await websocket.send(json.dumps(message))
            print("📤 Sent customer message (should trigger notification)")
            
            # Wait for response
            try:
                response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                print(f"📨 Chat response: {response[:100]}...")
            except asyncio.TimeoutError:
                print("⏰ No chat response received")
            
    except Exception as e:
        print(f"❌ Chat WebSocket test failed: {e}")

async def main():
    """Main test function"""
    print("🚀 Starting Notification System Tests")
    print("=" * 50)
    
    # Test 1: WebSocket connection
    await test_notification_websocket()
    
    # Test 2: Send test notification
    await send_test_notification()
    
    # Wait a bit
    await asyncio.sleep(2)
    
    # Test 3: Chat message notification
    await test_chat_notifications()
    
    print("\n" + "=" * 50)
    print("🏁 Notification tests completed!")
    print("\nTo see notifications in real-time:")
    print("1. Open the frontend: http://localhost:5174/")
    print("2. Login as admin")
    print("3. Open browser console to see WebSocket messages")
    print("4. Send messages via the chat system")

if __name__ == "__main__":
    asyncio.run(main())
